<% content_for :title, "AIエージェント管理システム - #{@school.name}" %>

<%= render 'admin/shared/ai_tutor_styles' %>

<style>
  /* Section Cards */
  .section-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    height: 100%;
  }

  /* Section Header */
  .section-header {
    background: #f7fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .section-icon {
    width: 40px;
    height: 40px;
    background: var(--accent-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
  }

  .section-info h3.section-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    text-transform: capitalize;
  }

  .section-subtitle {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.85rem;
  }

  .section-stats {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-active {
    background: var(--success-color);
  }
  .status-inactive {
    background: var(--text-secondary);
  }

  /* Section Body */
  .section-body {
    padding: 1.5rem 1.5rem 0 1.5rem;
  }

  .form-control {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
    font-size: 0.875rem;
  }

  .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
  }

  .prompt-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    min-height: 200px;
    background: #f8fafc;
  }

  .btn {
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
  }

  .btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
  }

  .variables-container {
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.8rem;
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
  }

  .variable-item {
    display: flex;
    align-items: flex-start;
    padding: 0.25rem 0;
    border-bottom: 1px solid #e2e8f0;
  }

  .variable-item:last-child {
    border-bottom: none;
  }

  .variable-item code {
    min-width: 120px;
    font-size: 0.75rem;
    font-weight: 600;
    background: var(--accent-color);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .variable-item code:hover {
    background: #2c5282;
  }

  .variable-item span {
    flex: 1;
    line-height: 1.3;
    color: var(--text-secondary);
    margin-left: 0.5rem;
  }

  /* Form Controls - Keep Bootstrap defaults */
  .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }

  /* Two Column Layout */
  .two-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .left-column {
    padding-right: 1rem;
  }

  .right-column {
    padding-left: 2rem;
    display: flex;
    flex-direction: column;
  }

  /* Prompt Preview Styles */
  .prompt-preview {
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .prompt-preview .card {
    border: 2px solid #17a2b8;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.15);
  }

  .prompt-preview .prompt-content {
    max-height: 300px;
    overflow-y: auto;
    line-height: 1.5;
    font-size: 0.9rem;
  }

  /* Playground Styles */
  .playground-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
  }

  .playground-header {
    background: #f7fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .playground-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
    color: var(--text-primary);
  }

  .playground-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .playground-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .lesson-selector-area {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
  }

  .lesson-selector-area .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .lesson-selector-area .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
  }

  .lesson-selector-area .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: white;
    height: 50vh;
    max-height: 50vh;
  }

  .debug-panel {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .debug-logs {
    background: #f8f9fa;
    border-radius: 4px;
    overflow-y: auto;
    height: calc(50vh + 85px);
    max-height: calc(50vh + 85px);
  }

  .debug-log-entry {
    background: white;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .debug-log-entry:hover {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }



  .chat-input-area {
    border-top: 1px solid var(--border-color);
    padding: 1rem;
    background: var(--bg-secondary);
    height: 77px;
  }

  .chat-input-wrapper {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
  }

  .chat-input {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
    font-size: 0.875rem;
    resize: none;
    min-height: 40px;
    max-height: 120px;
  }

  .chat-send-btn {
    background: var(--accent-color);
    border: none;
    color: white;
    border-radius: 6px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    white-space: nowrap;
  }

  .chat-send-btn:hover {
    background: #2c5282;
  }

  .chat-send-btn:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
  }

  /* Message Styles */
  .message {
    margin-bottom: 1rem;
    display: flex;
    gap: 0.75rem;
  }

  .message.user {
    flex-direction: row-reverse;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
  }

  .message.user .message-avatar {
    background: var(--accent-color);
    color: white;
  }

  .message.assistant .message-avatar {
    background: #e2e8f0;
    color: var(--text-primary);
  }

  .message-content {
    flex: 1;
    max-width: 80%;
  }

  .message-bubble {
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-size: 0.875rem;
    line-height: 1.4;
  }

  .message.user .message-bubble {
    background: var(--accent-color);
    color: white;
    margin-left: auto;
  }

  .message.assistant .message-bubble {
    background: #f1f5f9;
    color: var(--text-primary);
  }

  .message-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.25rem;
  }

  .message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
  }

  .message.user .message-actions {
    justify-content: flex-end;
  }

  .message.assistant .message-actions {
    justify-content: space-between;
  }

  /* System Prompt Button */
  .system-prompt-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 0.75rem;
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.2s ease;
  }

  .system-prompt-btn:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.05);
    color: var(--accent-color);
  }

  .system-prompt-btn i {
    font-size: 0.8rem;
  }

  /* Empty State */
  .chat-empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
  }

  .chat-empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .two-column-layout {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .right-column {
      border-left: none;
      border-top: 1px solid var(--border-color);
      padding-left: 0;
      padding-top: 2rem;
      margin-top: 2rem;
      max-height: calc(100vh - 200px);
    }
  }

  @media (max-width: 768px) {
    .content-area {
      padding: 1rem;
    }
    .top-nav {
      padding: 1rem;
    }
    .section-header {
      padding: 1rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    .section-title-wrapper {
      gap: 0.75rem;
    }
    .section-icon {
      width: 35px;
      height: 35px;
      font-size: 1rem;
    }
    .section-body {
      padding: 1rem;
    }

    .two-column-layout {
      height: auto;
    }

    .playground-container {
      height: 500px;
    }
  }

  @media (max-width: 576px) {
    .section-title-wrapper {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
</style>

<div class="main-container">
  <!-- Top Navigation -->
  <%= render 'admin/shared/ai_tutor_agent_detail_navigation', current_page: 'prompts' %>

  <!-- Content Area -->
  <div class="content-area">
    <div class="tab-content" id="mainTabsContent">
      <!-- プロンプト管理 -->
      <div class="tab-pane fade show active" id="prompts" role="tabpanel">
        <!-- Two Column Layout -->
        <div class="two-column-layout">
          <!-- Left Column: Tabs for Prompt Management & Debug -->
          <div class="left-column">
            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-3" id="leftColumnTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="system-prompt-tab" data-bs-toggle="tab" data-bs-target="#system-prompt-panel" type="button" role="tab">
                  <i class="bi bi-gear"></i> System Prompt
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="debug-tab" data-bs-toggle="tab" data-bs-target="#debug-panel-tab" type="button" role="tab">
                  <i class="bi bi-bug"></i> Function Debug
                </button>
              </li>
            </ul>

            <div class="tab-content" id="leftColumnTabsContent">
              <div class="tab-pane fade show active" id="system-prompt-panel" role="tabpanel">
                <% @prompt_types.each_with_index do |prompt_type, index| %>
                  <% prompt = @prompts_by_type[prompt_type] %>
                  <% next unless prompt_type == 'lesson' %>

                  <div class="section-card">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">
                      <i class="bi bi-book"></i>
                    </div>
                    <div class="section-info">
                      <h3 class="section-title"><%= prompt_type.humanize %> プロンプト</h3>
                      <p class="section-subtitle">
                        <%= case prompt_type
                            when 'lesson' then 'レッスン用のシステムプロンプト設定'
                            when 'chat' then 'チャット用のシステムプロンプト設定'
                            when 'analysis' then '分析用のシステムプロンプト設定'
                            else 'その他のプロンプト設定'
                            end %>
                      </p>
                    </div>
                  </div>
                </div>

                <div class="section-body">

                    <%= form_with url: update_content_admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent),
                                  method: :patch,
                                  local: false,
                                  html: { class: 'prompt-form', data: { prompt_type: prompt_type } } do |form| %>
                      <%= form.hidden_field :prompt_type, value: prompt_type %>

                      <%= form.hidden_field :enabled, value: 'true' %>

                      <div class="mb-3">
                        <%= form.text_area :content,
                            class: 'form-control prompt-textarea',
                            rows: 8,
                            placeholder: 'システムプロンプトを入力...',
                            value: prompt&.content || AiTutorPrompt.default_content_for(prompt_type),
                            data: { prompt_type: prompt_type } %>
                      </div>

                      <div class="mb-3">
                        <small class="text-muted">
                          <strong>利用可能な変数:</strong>
                          <span class="badge bg-info ms-2">クリックで挿入</span>
                        </small>
                        <div class="variables-container mt-2">
                          <% AiTutorPrompt.new(prompt_type: prompt_type).available_variables_with_descriptions.each do |var, description| %>
                            <div class="variable-item">
                              <code class="variable-clickable"
                                    data-variable="<%= var %>"
                                    data-prompt-type="<%= prompt_type %>"
                                    title="クリックしてテキストエリアに挿入">{{<%= var %>}}</code>
                              <span><%= description %></span>
                            </div>
                          <% end %>
                        </div>
                      </div>

                      <div class="d-flex gap-2 mb-2">
                        <button type="submit" class="btn btn-success btn-sm">
                          <i class="bi bi-check-circle"></i>保存
                        </button>
                      </div>
                    <% end %>
                </div>
              </div>
            <% end %>
              </div>

              <div class="tab-pane fade" id="debug-panel-tab" role="tabpanel">
                <div class="card border-warning">
                  <div class="card-header bg-warning text-dark py-2">
                    <div class="d-flex justify-content-between align-items-center">
                      <h6 class="mb-0">
                        <i class="bi bi-bug"></i> Chat Debug
                      </h6>
                      <div>
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="loadDebugInfo(true)">
                          <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-secondary me-2" id="auto-scroll-toggle" onclick="toggleAutoScroll()">
                          <i class="bi bi-arrow-down-circle"></i> Auto Scroll: ON
                        </button>
                        <button class="btn btn-sm btn-outline-dark" onclick="clearDebugLogs()">
                          <i class="bi bi-trash"></i> Clear
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="card-body p-2">
                    <div id="debug-logs" class="debug-logs" style="overflow-y: auto; font-family: monospace; font-size: 0.8rem;">
                      <div class="text-muted">Chat debug logs will appear here...</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="right-column">
            <div class="playground-container">
              <div class="playground-header">
                <h3 class="playground-title">
                  <i class="bi bi-chat-dots me-2"></i>
                  プロンプトテスト
                </h3>
                <div class="playground-controls">
                  <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-chat-btn">
                    <i class="bi bi-trash"></i>
                    クリア
                  </button>
                </div>
              </div>

              <div class="playground-chat">
                <div class="lesson-selector-area mb-3">
                  <div class="row align-items-center">
                    <div class="col-md-6">
                      <label for="course-selector" class="form-label mb-1">
                        <i class="bi bi-collection"></i> コース選択
                      </label>
                      <select class="form-select" id="course-selector">
                        <option value="">コースを選択してください...</option>
                        <% @school.courses.published.order(:name).each do |course| %>
                          <option value="<%= course.id %>"><%= course.name %></option>
                        <% end %>
                      </select>
                    </div>

                    <div class="col-md-6">
                      <label for="lesson-selector" class="form-label mb-1">
                        <i class="bi bi-book"></i> レッスン選択
                      </label>
                      <select class="form-select" id="lesson-selector" disabled>
                        <option value="">まずコースを選択してください...</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                  <div class="chat-empty-state">
                    <i class="bi bi-chat-text"></i>
                    <p>プロンプトをテストするために質問を入力してください</p>
                    <small>左側でプロンプトを編集すると、ここでリアルタイムでテストできます</small>
                  </div>
                </div>

                <div class="chat-input-area">
                  <div class="chat-input-wrapper">
                    <textarea
                      class="chat-input"
                      id="chat-input"
                      placeholder="質問を入力してください..."
                      rows="1"></textarea>
                    <button type="button" class="chat-send-btn" id="chat-send-btn">
                      <i class="bi bi-send"></i>
                      送信
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="testResultModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">プロンプトテスト結果</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="test-result-content"></div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.prompt-form').forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const promptType = form.dataset.promptType;
      const textarea = form.querySelector('textarea[name="content"]');

      const formData = new FormData();
      formData.append('prompt_type', promptType);
      formData.append('content', textarea.value);
      formData.append('enabled', 'true');

      fetch('<%= update_content_admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent) %>', {
        method: 'PATCH',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          showAlert('success', 'プロンプトが正常に保存されました');
        } else {
          showAlert('error', data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'エラーが発生しました');
      });
    });
  });

  document.querySelectorAll('.variable-clickable').forEach(variable => {
    variable.addEventListener('click', function() {
      const variableName = this.dataset.variable;
      const promptType = this.dataset.promptType;
      const textarea = document.querySelector(`textarea[data-prompt-type="${promptType}"]`);

      if (textarea) {
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(textarea.selectionEnd);
        const variableText = `{{${variableName}}}`;

        textarea.value = textBefore + variableText + textAfter;
        textarea.focus();
        textarea.setSelectionRange(cursorPos + variableText.length, cursorPos + variableText.length);

        this.style.backgroundColor = '#2c5282';
        setTimeout(() => {
          this.style.backgroundColor = '';
        }, 200);
      }
    });
  });

  function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-danger';

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
      <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'x-circle'} me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.content-area');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
      alertDiv.remove();
    }, 5000);
  }

  function showTestResult(renderedContent, testData) {
    const modalContent = document.getElementById('test-result-content');
    modalContent.innerHTML = `
      <div class="mb-3">
        <h6>テストデータ:</h6>
        <pre class="bg-light p-2 rounded"><code>${JSON.stringify(testData, null, 2)}</code></pre>
      </div>
      <div class="mb-3">
        <h6>レンダリング結果:</h6>
        <div class="border p-3 rounded bg-light">
          <pre style="white-space: pre-wrap; margin: 0;">${renderedContent}</pre>
        </div>
      </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('testResultModal'));
    modal.show();
  }

  // Playground Chat Functionality
  const chatMessages = document.getElementById('chat-messages');
  const chatInput = document.getElementById('chat-input');
  const chatSendBtn = document.getElementById('chat-send-btn');
  const clearChatBtn = document.getElementById('clear-chat-btn');
  const courseSelector = document.getElementById('course-selector');
  const lessonSelector = document.getElementById('lesson-selector');
  let isGenerating = false;
  let currentSessionId = generateSessionId();
  let playgroundSubscription = null;

  // Generate unique session ID
  function generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Clear cache when entering the page
  function clearPageCache() {
    // Clear localStorage system prompt cache
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key) && key.startsWith("system_prompt")) {
        localStorage.removeItem(key);
      }
    }

    // Clear debug logs cache
    window.debugLogs = [];
    const debugLogsContainer = document.getElementById('debug-logs');
    if (debugLogsContainer) {
      debugLogsContainer.innerHTML = '<div class="text-muted">Function calling logs will appear here...</div>';
    }

    console.log('Page cache cleared');
  }

  // Initialize ActionCable connection for playground
  function initializePlaygroundConnection() {
    if (typeof App !== 'undefined' && App.cable) {
      console.log('Subscribing to playground channel with session_id:', currentSessionId);
      playgroundSubscription = App.cable.subscriptions.create(
        {
          channel: "PlaygroundChatChannel",
          session_id: currentSessionId
        },
        {
          connected() {
            console.log("Playground chat connected with session:", currentSessionId);
          },

          disconnected() {
            console.log("Playground chat disconnected");
          },

          received(data) {
            console.log('ActionCable received data:', data);
            handlePlaygroundMessage(data);
          }
        }
      );
    } else {
      console.error('ActionCable not available');
    }
  }

  function handlePlaygroundMessage(data) {
    console.log('Received playground message:', data);

    if (data.type === 'system_prompt') {
      storeSystemPrompt(data.message_id, data.system_prompt);
    } else if (data.message_id && data.content && data.content !== 'Finished') {
      updateMessage(data.message_id, data.content);
    } else if (data.content === 'Finished') {
      isGenerating = false;
      chatSendBtn.disabled = false;
      chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';

      setTimeout(() => chatInput.focus(), 400);
    } else if (data.type === 'error') {
      const messageId = data.message_id || document.querySelector('.message.assistant:last-child')?.id;
      if (messageId) {
        updateMessage(messageId, `エラー: ${data.message}`);
      }
      showAlert('error', data.message);

      isGenerating = false;
      chatSendBtn.disabled = false;
      chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';
    }
  }

  // Clear cache and initialize connection when page loads
  clearPageCache();
  initializePlaygroundConnection();

  // Clear cache when leaving the page
  window.addEventListener('beforeunload', function() {
    clearPageCache();
  });

  chatInput.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
  });

  let isComposing = false;

  chatInput.addEventListener('compositionstart', function(e) {
    isComposing = true;
  });

  chatInput.addEventListener('compositionend', function(e) {
    isComposing = false;
  });

  chatInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      sendMessage();
    }
  });

  chatSendBtn.addEventListener('click', sendMessage);

  clearChatBtn.addEventListener('click', function() {
    currentSessionId = generateSessionId();

    // Clear cache when resetting chat
    clearPageCache();

    if (playgroundSubscription) {
      playgroundSubscription.unsubscribe();
    }
    initializePlaygroundConnection();

    chatMessages.innerHTML = `
      <div class="chat-empty-state">
        <i class="bi bi-chat-text"></i>
        <p>プロンプトをテストするために質問を入力してください</p>
        <small>左側でプロンプトを編集すると、ここでリアルタイムでテストできます</small>
      </div>
    `;

    isGenerating = false;
    chatSendBtn.disabled = false;
    chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';
  });

  function sendMessage() {
    const message = chatInput.value.trim();
    if (!message || isGenerating) return;

    const promptTextarea = document.querySelector('textarea[data-prompt-type="lesson"]');
    const promptContent = promptTextarea ? promptTextarea.value : '';

    if (!promptContent.trim()) {
      showAlert('warning', 'プロンプト内容を入力してください');
      return;
    }

    const emptyState = chatMessages.querySelector('.chat-empty-state');
    if (emptyState) {
      chatMessages.innerHTML = '';
    }

    addMessage('user', message);

    chatInput.value = '';
    chatInput.style.height = 'auto';

    const tempAssistantId = addMessage('assistant', '');

    isGenerating = true;
    chatSendBtn.disabled = true;
    chatSendBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 生成中...';

    sendPlaygroundRequest(message, promptContent, tempAssistantId);
  }

  function addMessage(role, content) {
    const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const time = new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' });

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    messageDiv.id = messageId;

    const avatar = role === 'user' ? 'U' : 'AI';
    const avatarClass = role === 'user' ? 'user' : 'assistant';

    messageDiv.innerHTML = `
      <div class="message-avatar">${avatar}</div>
      <div class="message-content">
        <div class="message-bubble">
          <div class="message-text">${content}</div>
        </div>
        <div class="message-actions">
          <div class="message-time">${time}</div>
        </div>
      </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageId;
  }

  function updateMessage(messageId, content) {
    const messageElement = document.getElementById(messageId);
    if (messageElement) {
      const textElement = messageElement.querySelector('.message-text');
      if (textElement) {
        textElement.innerHTML = content;
        chatMessages.scrollTop = chatMessages.scrollHeight;
      } else {
        console.error('Text element not found in message:', messageId);
      }
    } else {
      console.error('Message element not found:', messageId);
    }
  }

  function sendPlaygroundRequest(userMessage, promptContent, assistantMessageId) {
    const selectedCourseId = courseSelector.value;
    const selectedLessonId = lessonSelector.value;

    fetch('<%= playground_chat_admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent) %>', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({
        message: userMessage,
        prompt_content: promptContent,
        prompt_type: 'lesson',
        session_id: currentSessionId,
        course_id: selectedCourseId || null,
        lesson_id: selectedLessonId || null
      })
    })
    .then(response => {
      return response.json();
    })
    .then(data => {
      if (data.status === 'success') {
        const assistantMessage = document.getElementById(assistantMessageId);
        if (assistantMessage) {
          assistantMessage.id = data.bot_message_id;
        } else {
          console.error('Assistant message not found:', assistantMessageId);
        }
        console.log('Playground chat started:', data);
      } else {
        updateMessage(assistantMessageId, `エラー: ${data.message}`);
        showAlert('error', data.message);

        isGenerating = false;
        chatSendBtn.disabled = false;
        chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';
      }
    })
    .catch(error => {
      console.error('Playground chat error:', error);
      updateMessage(assistantMessageId, 'エラーが発生しました。もう一度お試しください。');
      showAlert('error', 'チャット処理中にエラーが発生しました');

      isGenerating = false;
      chatSendBtn.disabled = false;
      chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';
    });
  }

  courseSelector.addEventListener('change', function() {
    const courseId = this.value;

    lessonSelector.innerHTML = '<option value="">レッスンを読み込み中...</option>';
    lessonSelector.disabled = true;

    if (courseId) {
      fetch(`/admin/schools/<%= @school.id %>/courses/${courseId}/course_lessons.json`)
        .then(response => response.json())
        .then(lessons => {
          lessonSelector.innerHTML = '<option value="">レッスンを選択してください...</option>';

          lessons.forEach(lesson => {
            const option = document.createElement('option');
            option.value = lesson.id;
            option.textContent = lesson.name;
            lessonSelector.appendChild(option);
          });

          lessonSelector.disabled = false;
        })
        .catch(error => {
          console.error('Error loading lessons:', error);
          lessonSelector.innerHTML = '<option value="">レッスンの読み込みに失敗しました</option>';
        });
    } else {
      lessonSelector.innerHTML = '<option value="">まずコースを選択してください...</option>';
      lessonSelector.disabled = true;
    }
  });

  window.debugLogs = [];
  window.autoScrollEnabled = true; // Default: enabled

  // Toggle auto-scroll functionality
  window.toggleAutoScroll = function() {
    window.autoScrollEnabled = !window.autoScrollEnabled;
    const toggleBtn = document.getElementById('auto-scroll-toggle');

    if (window.autoScrollEnabled) {
      toggleBtn.innerHTML = '<i class="bi bi-arrow-down-circle-fill"></i> Auto Scroll: ON';
      toggleBtn.className = 'btn btn-sm btn-outline-success me-2';

      // Scroll to bottom when enabling
      const debugLogsContainer = document.getElementById('debug-logs');
      if (debugLogsContainer) {
        debugLogsContainer.scrollTop = debugLogsContainer.scrollHeight;
      }
    } else {
      toggleBtn.innerHTML = '<i class="bi bi-arrow-down-circle"></i> Auto Scroll: OFF';
      toggleBtn.className = 'btn btn-sm btn-outline-secondary me-2';
    }
  };

  // Initialize auto-scroll button state
  document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('auto-scroll-toggle');
    if (toggleBtn && window.autoScrollEnabled) {
      toggleBtn.innerHTML = '<i class="bi bi-arrow-down-circle-fill"></i> Auto Scroll: ON';
      toggleBtn.className = 'btn btn-sm btn-outline-success me-2';
    }
  });

  document.getElementById('debug-tab').addEventListener('shown.bs.tab', function() {
    loadDebugInfo(true);
  });

  window.clearDebugLogs = function() {
    fetch(`/admin/schools/<%= @school.id %>/agents/<%= @ai_tutor_agent.id %>/prompts/clear_debug`, {
      method: 'DELETE',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    }).then(() => {
      window.debugLogs = [];
      const debugLogsContainer = document.getElementById('debug-logs');
      debugLogsContainer.innerHTML = '<div class="text-muted">Function calling logs will appear here...</div>';
    }).catch(error => {
      console.error('Failed to clear server debug logs:', error);
      window.debugLogs = [];
      const debugLogsContainer = document.getElementById('debug-logs');
      debugLogsContainer.innerHTML = '<div class="text-muted">Function calling logs will appear here...</div>';
    });
  };

  window.addDebugLog = function(type, message, data = null) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      timestamp,
      type,
      message,
      data
    };

    window.debugLogs.push(logEntry);

    const debugLogsContainer = document.getElementById('debug-logs');
    if (!debugLogsContainer) return;

    const placeholderElement = debugLogsContainer.querySelector('.text-muted');
    const isActualPlaceholder = placeholderElement &&
      (placeholderElement.textContent.includes('Function calling logs will appear here') ||
       placeholderElement.textContent.includes('No debug logs available'));

    if (isActualPlaceholder) {
      debugLogsContainer.innerHTML = '';
    }

    const logElement = document.createElement('div');
    logElement.className = 'debug-log-entry mb-2 p-2 border-start border-3';

    let borderColor = 'border-secondary';
    let icon = 'bi-info-circle';

    switch(type) {
      case 'tool_added':
        borderColor = 'border-primary';
        icon = 'bi-wrench';
        break;
      case 'tool_created':
        borderColor = 'border-success';
        icon = 'bi-gear';
        break;
      case 'tool_executed':
        borderColor = 'border-warning';
        icon = 'bi-play-circle';
        break;
      case 'tool_result':
        borderColor = 'border-info';
        icon = 'bi-check-circle';
        break;
      case 'system_prompt':
        borderColor = 'border-info';
        icon = 'bi-file-text';
        break;
      case 'user_input':
        borderColor = 'border-primary';
        icon = 'bi-chat-quote';
        break;
      case 'error':
        borderColor = 'border-danger';
        icon = 'bi-exclamation-triangle';
        break;
      default:
        borderColor = 'border-secondary';
        icon = 'bi-info-circle';
    }

    logElement.classList.add(borderColor);

    let logHtml = `
      <div class="d-flex align-items-start">
        <i class="bi ${icon} me-2 mt-1"></i>
        <div class="flex-grow-1">
          <div class="d-flex justify-content-between">
            <strong class="text-${type === 'error' ? 'danger' : 'dark'}">${message}</strong>
            <small class="text-muted">${timestamp}</small>
          </div>
    `;



    if (data) {
      logHtml += `
          <div class="mt-1">
            <small class="text-muted">Data:</small>
            <div class="mt-1">
              <pre class="mb-0 bg-light p-2 rounded" style="font-size: 0.7rem; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>
            </div>
          </div>
      `;
    }

    logHtml += `
        </div>
      </div>
    `;

    logElement.innerHTML = logHtml;
    debugLogsContainer.appendChild(logElement);

    // Auto scroll to bottom only if auto-scroll is enabled
    if (window.autoScrollEnabled) {
      debugLogsContainer.scrollTop = debugLogsContainer.scrollHeight;
    }
  };

  window.loadDebugInfo = function(forceRefresh = false) {
    fetch(`/admin/schools/<%= @school.id %>/agents/<%= @ai_tutor_agent.id %>/prompts/debug_info`)
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          const debugLogsContainer = document.getElementById('debug-logs');

          window.debugLogs = [];
          debugLogsContainer.innerHTML = '';

          data.debug_logs.forEach(log => {
            window.addDebugLog(log.type, log.message, log.data);
          });

          if (window.debugLogs.length === 0) {
            debugLogsContainer.innerHTML = '<div class="text-muted">No debug logs available</div>';
          } else if (forceRefresh && window.autoScrollEnabled) {
            // Only scroll to bottom on force refresh if auto-scroll is enabled
            debugLogsContainer.scrollTop = debugLogsContainer.scrollHeight;
          }
        }
      })
      .catch(error => {
        console.error('Failed to load debug info:', error);
      });
  };

  // Auto-refresh debug info every 5 seconds when debug tab is active
  setInterval(() => {
    const debugTab = document.getElementById('debug-panel-tab');
    if (debugTab && debugTab.classList.contains('active')) {
      loadDebugInfo();
    }
  }, 5000);
});
</script>
