<% content_for :title, "AIエージェント管理システム - #{@school.name}" %>

<%= render 'admin/shared/ai_tutor_styles' %>

<style>
  /* Section Cards */
  .section-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    height: 100%;
  }

  /* Section Header */
  .section-header {
    background: #f7fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .section-icon {
    width: 40px;
    height: 40px;
    background: var(--accent-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
  }

  .section-info h3.section-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    text-transform: capitalize;
  }

  .section-subtitle {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.85rem;
  }

  .section-stats {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-active {
    background: var(--success-color);
  }
  .status-inactive {
    background: var(--text-secondary);
  }

  /* Section Body */
  .section-body {
    padding: 1.5rem 1.5rem 0 1.5rem;
  }

  .form-control {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
    font-size: 0.875rem;
  }

  .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
  }

  .prompt-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    min-height: 200px;
    background: #f8fafc;
  }

  .btn {
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
  }

  .btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
  }

  .variables-container {
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.8rem;
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
  }

  .variable-item {
    display: flex;
    align-items: flex-start;
    padding: 0.25rem 0;
    border-bottom: 1px solid #e2e8f0;
  }

  .variable-item:last-child {
    border-bottom: none;
  }

  .variable-item code {
    min-width: 120px;
    font-size: 0.75rem;
    font-weight: 600;
    background: var(--accent-color);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .variable-item code:hover {
    background: #2c5282;
  }

  .variable-item span {
    flex: 1;
    line-height: 1.3;
    color: var(--text-secondary);
    margin-left: 0.5rem;
  }

  /* Form Controls - Keep Bootstrap defaults */
  .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }

  /* Two Column Layout */
  .two-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .left-column {
    padding-right: 1rem;
  }

  .right-column {
    padding-left: 2rem;
    display: flex;
    flex-direction: column;
  }

  /* Prompt Preview Styles */
  .prompt-preview {
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .prompt-preview .card {
    border: 2px solid #17a2b8;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.15);
  }

  .prompt-preview .prompt-content {
    max-height: 300px;
    overflow-y: auto;
    line-height: 1.5;
    font-size: 0.9rem;
  }

  /* Playground Styles */
  .playground-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
  }

  .playground-header {
    background: #f7fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .playground-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
    color: var(--text-primary);
  }

  .playground-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .playground-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .lesson-selector-area {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
  }

  .lesson-selector-area .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .lesson-selector-area .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
  }

  .lesson-selector-area .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: white;
    height: 50vh;
    max-height: 50vh;
  }

  .debug-panel {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .debug-logs {
    background: #f8f9fa;
    border-radius: 4px;
    overflow-y: auto;
    height: calc(50vh + 97px + 56px); /* 97px is the height of the chat-input-area, 56px is the height of the chat-input-wrapper */
    max-height: calc(50vh + 97px + 56px); 
    max-width: calc(50vw - 150px);
  }

  .debug-log-entry {
    background: white;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .debug-log-entry:hover {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }



  .chat-input-area {
    border-top: 1px solid #d9d9e3;
    padding: 20px 10px;
    background: #ffffff;
    min-height: 77px;
  }

  .chat-input-wrapper {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
    max-width: 100%;
    width: 100%;
  }

  .chat-input-container {
    display: flex;
    width: 100%;
    align-items: center;
  }

  .chat-input-container.disabled {
    opacity: 0.6;
    background-color: #f0f0f0;
    cursor: not-allowed;
  }

  .chat-input {
    flex: 1;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    resize: none;
    color: #343541;
    overflow-y: auto;
  }

  .chat-input:focus {
    outline: 2px solid var(--accent-color);
  }

  .chat-input::placeholder {
    color: #a9a9bc;
  }

  .chat-input.disabled {
    cursor: not-allowed;
    background-color: #f0f0f0;
  }

  .chat-send-btn {
    background: transparent;
    border: none;
    color: #5f6368;
    border-radius: 50%;
    padding: 8px;
    font-size: 1.25rem;
    cursor: pointer;
    white-space: nowrap;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    transition: all 0.2s ease;
  }

  .chat-send-btn:hover {
    background: rgba(95, 99, 104, 0.1);
  }

  .chat-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .chat-send-btn.active {
    background: var(--accent-color);
    color: white;
  }

  .chat-send-btn.active:hover {
    background: #2c5282;
  }

  /* File attachment button - Match classroom UI */
  .attachment-btn {
    background: transparent;
    border: none;
    color: #5f6368;
    border-radius: 50%;
    padding: 8px;
    font-size: 1.25rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
  }

  .attachment-btn:hover {
    background: rgba(95, 99, 104, 0.1);
  }

  .attachment-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* File attachments display */
  .chat-input-attachments {
    padding: 1rem 0 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0.5rem;
  }

  .attachments-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .attachment-item {
    display: flex;
    align-items: center;
    background: #f1f3f4;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 8px 12px;
    position: relative;
    margin-bottom: 4px;
  }

  .attachment-icon {
    width: 24px;
    height: 24px;
    background: #34a853;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .attachment-info {
    flex: 1;
    min-width: 0;
  }

  .attachment-name {
    font-weight: 500;
    font-size: 0.8rem;
    color: #3c4043;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .attachment-size {
    font-size: 0.7rem;
    color: #5f6368;
  }

  .attachment-remove {
    background: none;
    border: none;
    color: #5f6368;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: background-color 0.2s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .attachment-remove:hover {
    background: rgba(95, 99, 104, 0.1);
  }

  /* Responsive Design */
  @media screen and (max-width: 1200px) {
    .two-column-layout {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .right-column {
      padding-left: 0;
    }

    .left-column {
      padding-right: 0;
    }
  }

  @media screen and (max-width: 768px) {
    .playground-container {
      border-radius: 0;
      border-left: none;
      border-right: none;
    }

    .chat-messages {
      height: 40vh;
      max-height: 40vh;
    }

    .debug-logs {
      height: calc(40vh + 85px);
      max-height: calc(40vh + 85px);
    }

    .lesson-selector-area .row {
      flex-direction: column;
    }

    .lesson-selector-area .col-md-6 {
      width: 100%;
      margin-bottom: 1rem;
    }

    .lesson-selector-area .col-md-6:last-child {
      margin-bottom: 0;
    }
  }

  @media screen and (max-width: 600px) {
    .message {
      padding: 0.25rem 0.5rem;
    }

    .message-avatar {
      width: 28px;
      height: 28px;
      font-size: 0.7rem;
    }

    .message-content {
      max-width: 80%;
    }

    .message-bubble {
      font-size: 0.9rem;
      padding: 10px 14px;
    }

    .chat-input-area {
      padding: 15px 10px;
    }

    .chat-input {
      min-height: 45px;
      padding: 10px 40px 10px 10px;
    }

    .chat-send-btn, .attachment-btn {
      width: 45px;
      height: 45px;
      font-size: 1.25rem;
    }

    .attachment-item {
      padding: 0.5rem;
    }

    .attachment-icon {
      width: 28px;
      height: 28px;
      font-size: 0.75rem;
    }

    .attachment-name {
      font-size: 0.8rem;
    }

    .attachment-size {
      font-size: 0.7rem;
    }

    .message-attachment-image {
      height: 200px !important;
    }
  }

  /* Drag and drop styles */
  .chat-input-area.drag-over {
    background: #e3f2fd;
    border: 2px dashed var(--accent-color);
    border-radius: 8px;
  }

  .chat-input-area.drag-over::before {
    content: "ファイルをドロップしてください";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    color: var(--accent-color);
    z-index: 10;
    pointer-events: none;
  }

  /* Message Styles - Chat bubble design */
  .message {
    margin-bottom: 1rem;
    display: flex;
    gap: 0.5rem;
    padding: 0.25rem 1rem;
    align-items: flex-end;
  }

  .message.user {
    flex-direction: row-reverse;
    justify-content: flex-start;
  }

  .message.assistant {
    justify-content: flex-start;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    flex-shrink: 0;
    margin-bottom: 2px;
  }

  .message.user .message-avatar {
    background: var(--accent-color);
    color: white;
  }

  .message.assistant .message-avatar {
    background: #6c757d;
    color: white;
  }

  .message-content {
    max-width: 70%;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .message.user .message-content {
    align-items: flex-end;
  }

  .message.assistant .message-content {
    align-items: flex-start;
  }

  .message-bubble {
    padding: 12px 16px;
    font-size: 0.95rem;
    line-height: 1.4;
    word-break: break-word;
    display: inline-block;
    max-width: 100%;
    position: relative;
  }

  .message.user .message-bubble {
    background: var(--accent-color);
    color: white;
    border-radius: 18px 18px 4px 18px;
  }

  .message.assistant .message-bubble {
    background: #f1f3f4;
    color: #3c4043;
    border-radius: 18px 18px 18px 4px;
    border: 1px solid #e8eaed;
  }

  .message-time {
    font-size: 0.7rem;
    color: #9aa0a6;
    padding: 0 4px;
  }

  /* Message attachment styles */
  .message-attachment-image {
    max-width: 280px !important;
    max-height: 200px !important;
    width: auto !important;
    height: auto !important;
    margin: 8px 0 4px 0 !important;
    cursor: pointer;
    border-radius: 12px;
    display: block;
  }

  .chat-input-attachment-container {
    margin: 4px 0;
  }

  .chat-input-attachment {
    background: #f1f3f4;
    border: 1px solid #e8eaed;
    border-radius: 12px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: inline-block;
    max-width: 200px;
  }

  .chat-input-attachment:hover {
    background: #e9ecef;
  }

  .chat-input-attachment-title {
    font-size: 0.875rem;
    color: #495057;
  }

  .chat-input-attachment-ext {
    font-size: 0.75rem;
    color: #6c757d;
  }

  /* Typing Animation */
  .typing-animation {
    display: inline-flex;
    align-items: center;
    padding: 8px 0;
  }

  .typing-animation .typing-dot {
    height: 6px;
    width: 6px;
    border-radius: 50%;
    margin: 0 2px;
    opacity: 0.7;
    background: #9aa0a6;
  }

  .typing-animation .dot1 {
    animation: animateDots 1.5s 0.2s ease-in-out infinite;
  }

  .typing-animation .dot2 {
    animation: animateDots 1.5s 0.3s ease-in-out infinite;
  }

  .typing-animation .dot3 {
    animation: animateDots 1.5s 0.4s ease-in-out infinite;
  }

  .typing-animation .typing-dot:first-child {
    margin-left: 0;
  }

  @keyframes animateDots {
    0%,
    44% {
      transform: translateY(0px);
    }

    28% {
      opacity: 0.4;
      transform: translateY(-6px);
    }

    44% {
      opacity: 0.2;
    }
  }

  /* System Prompt Button */
  .system-prompt-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 0.75rem;
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.2s ease;
  }

  .system-prompt-btn:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.05);
    color: var(--accent-color);
  }

  .system-prompt-btn i {
    font-size: 0.8rem;
  }

  /* Empty State */
  .chat-empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
  }

  .chat-empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .two-column-layout {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .right-column {
      border-left: none;
      border-top: 1px solid var(--border-color);
      padding-left: 0;
      padding-top: 2rem;
      margin-top: 2rem;
      max-height: calc(100vh - 200px);
    }
  }

  @media (max-width: 768px) {
    .content-area {
      padding: 1rem;
    }
    .top-nav {
      padding: 1rem;
    }
    .section-header {
      padding: 1rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    .section-title-wrapper {
      gap: 0.75rem;
    }
    .section-icon {
      width: 35px;
      height: 35px;
      font-size: 1rem;
    }
    .section-body {
      padding: 1rem;
    }

    .two-column-layout {
      height: auto;
    }

    .playground-container {
      height: 500px;
    }
  }

  @media (max-width: 576px) {
    .section-title-wrapper {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
</style>

<div class="main-container">
  <!-- Top Navigation -->
  <%= render 'admin/shared/ai_tutor_agent_detail_navigation', current_page: 'prompts' %>

  <!-- Content Area -->
  <div class="content-area">
    <div class="tab-content" id="mainTabsContent">
      <!-- プロンプト管理 -->
      <div class="tab-pane fade show active" id="prompts" role="tabpanel">
        <!-- Two Column Layout -->
        <div class="two-column-layout">
          <!-- Left Column: Tabs for Prompt Management & Debug -->
          <div class="left-column">
            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-3" id="leftColumnTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="system-prompt-tab" data-bs-toggle="tab" data-bs-target="#system-prompt-panel" type="button" role="tab">
                  <i class="bi bi-gear"></i> System Prompt
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="debug-tab" data-bs-toggle="tab" data-bs-target="#debug-panel-tab" type="button" role="tab">
                  <i class="bi bi-bug"></i> Function Debug
                </button>
              </li>
            </ul>

            <div class="tab-content" id="leftColumnTabsContent">
              <div class="tab-pane fade show active" id="system-prompt-panel" role="tabpanel">
                <% @prompt_types.each_with_index do |prompt_type, index| %>
                  <% prompt = @prompts_by_type[prompt_type] %>
                  <% next unless prompt_type == 'lesson' %>

                  <div class="section-card">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">
                      <i class="bi bi-book"></i>
                    </div>
                    <div class="section-info">
                      <h3 class="section-title"><%= prompt_type.humanize %> プロンプト</h3>
                      <p class="section-subtitle">
                        <%= case prompt_type
                            when 'lesson' then 'レッスン用のシステムプロンプト設定'
                            when 'chat' then 'チャット用のシステムプロンプト設定'
                            when 'analysis' then '分析用のシステムプロンプト設定'
                            else 'その他のプロンプト設定'
                            end %>
                      </p>
                    </div>
                  </div>
                </div>

                <div class="section-body">

                    <%= form_with url: update_content_admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent),
                                  method: :patch,
                                  local: false,
                                  html: { class: 'prompt-form', data: { prompt_type: prompt_type } } do |form| %>
                      <%= form.hidden_field :prompt_type, value: prompt_type %>

                      <%= form.hidden_field :enabled, value: 'true' %>

                      <div class="mb-3">
                        <%= form.text_area :content,
                            class: 'form-control prompt-textarea',
                            rows: 8,
                            placeholder: 'システムプロンプトを入力...',
                            value: prompt&.content || AiTutorPrompt.default_content_for(prompt_type),
                            data: { prompt_type: prompt_type } %>
                      </div>

                      <div class="mb-3">
                        <small class="text-muted">
                          <strong>利用可能な変数:</strong>
                          <span class="badge bg-info ms-2">クリックで挿入</span>
                        </small>
                        <div class="variables-container mt-2">
                          <% AiTutorPrompt.new(prompt_type: prompt_type).available_variables_with_descriptions.each do |var, description| %>
                            <div class="variable-item">
                              <code class="variable-clickable"
                                    data-variable="<%= var %>"
                                    data-prompt-type="<%= prompt_type %>"
                                    title="クリックしてテキストエリアに挿入">{{<%= var %>}}</code>
                              <span><%= description %></span>
                            </div>
                          <% end %>
                        </div>
                      </div>

                      <div class="d-flex gap-2 mb-2">
                        <button type="submit" class="btn btn-success btn-sm">
                          <i class="bi bi-check-circle"></i>保存
                        </button>
                      </div>
                    <% end %>
                </div>
              </div>
            <% end %>
              </div>

              <div class="tab-pane fade" id="debug-panel-tab" role="tabpanel">
                <div class="card border-warning">
                  <div class="card-header bg-warning text-dark py-2">
                    <div class="d-flex justify-content-between align-items-center">
                      <h6 class="mb-0">
                        <i class="bi bi-bug"></i> Chat Debug
                      </h6>
                      <div>
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="loadDebugInfo(true)">
                          <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-secondary me-2" id="auto-scroll-toggle" onclick="toggleAutoScroll()">
                          <i class="bi bi-arrow-down-circle"></i> Auto Scroll: ON
                        </button>
                        <button class="btn btn-sm btn-outline-dark" onclick="clearDebugLogs()">
                          <i class="bi bi-trash"></i> Clear
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="card-body p-2">
                    <div id="debug-logs" class="debug-logs" style="overflow-y: auto; font-family: monospace; font-size: 0.8rem;">
                      <div class="text-muted">Chat debug logs will appear here...</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="right-column">
            <div class="playground-container">
              <div class="playground-header">
                <h3 class="playground-title">
                  <i class="bi bi-chat-dots me-2"></i>
                  プロンプトテスト
                </h3>
                <div class="playground-controls">
                  <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-chat-btn">
                    <i class="bi bi-trash"></i>
                    クリア
                  </button>
                </div>
              </div>

              <div class="playground-chat">
                <div class="lesson-selector-area mb-3">
                  <div class="row align-items-center">
                    <div class="col-md-6">
                      <label for="course-selector" class="form-label mb-1">
                        <i class="bi bi-collection"></i> コース選択
                      </label>
                      <select class="form-select" id="course-selector">
                        <option value="">コースを選択してください...</option>
                        <% @school.courses.published.order(:name).each do |course| %>
                          <option value="<%= course.id %>"><%= course.name %></option>
                        <% end %>
                      </select>
                    </div>

                    <div class="col-md-6">
                      <label for="lesson-selector" class="form-label mb-1">
                        <i class="bi bi-book"></i> レッスン選択
                      </label>
                      <select class="form-select" id="lesson-selector" disabled>
                        <option value="">まずコースを選択してください...</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                  <div class="chat-empty-state">
                    <i class="bi bi-chat-text"></i>
                    <p>プロンプトをテストするために質問を入力してください</p>
                    <small>左側でプロンプトを編集すると、ここでリアルタイムでテストできます</small>
                  </div>
                </div>

                <div class="chat-input-area">
                  <!-- File attachments display area -->
                  <div class="chat-input-attachments" id="chat-attachments" style="display: none;">
                    <div class="attachments-list" id="attachments-list"></div>
                  </div>

                  <div class="chat-input-wrapper">
                    <div class="chat-input-container">
                      <button type="button" class="attachment-btn" id="attachment-btn" title="画像を添付">
                        <i class="material-icons">attach_file</i>
                      </button>
                      <input type="file" id="file-input" style="display: none;" multiple accept=".jpg, .jpeg, .png">
                      <textarea
                        class="chat-input"
                        id="chat-input"
                        placeholder="内容を入力してください"
                        rows="1"></textarea>
                      <button type="button" class="chat-send-btn" id="chat-send-btn">
                        <i class="material-icons">send</i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="testResultModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">プロンプトテスト結果</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="test-result-content"></div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.prompt-form').forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const promptType = form.dataset.promptType;
      const textarea = form.querySelector('textarea[name="content"]');

      const formData = new FormData();
      formData.append('prompt_type', promptType);
      formData.append('content', textarea.value);
      formData.append('enabled', 'true');

      fetch('<%= update_content_admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent) %>', {
        method: 'PATCH',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          showAlert('success', 'プロンプトが正常に保存されました');
        } else {
          showAlert('error', data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'エラーが発生しました');
      });
    });
  });

  document.querySelectorAll('.variable-clickable').forEach(variable => {
    variable.addEventListener('click', function() {
      const variableName = this.dataset.variable;
      const promptType = this.dataset.promptType;
      const textarea = document.querySelector(`textarea[data-prompt-type="${promptType}"]`);

      if (textarea) {
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(textarea.selectionEnd);
        const variableText = `{{${variableName}}}`;

        textarea.value = textBefore + variableText + textAfter;
        textarea.focus();
        textarea.setSelectionRange(cursorPos + variableText.length, cursorPos + variableText.length);

        this.style.backgroundColor = '#2c5282';
        setTimeout(() => {
          this.style.backgroundColor = '';
        }, 200);
      }
    });
  });

  function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-danger';

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
      <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'x-circle'} me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.content-area');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
      alertDiv.remove();
    }, 5000);
  }

  function showTestResult(renderedContent, testData) {
    const modalContent = document.getElementById('test-result-content');
    modalContent.innerHTML = `
      <div class="mb-3">
        <h6>テストデータ:</h6>
        <pre class="bg-light p-2 rounded"><code>${JSON.stringify(testData, null, 2)}</code></pre>
      </div>
      <div class="mb-3">
        <h6>レンダリング結果:</h6>
        <div class="border p-3 rounded bg-light">
          <pre style="white-space: pre-wrap; margin: 0;">${renderedContent}</pre>
        </div>
      </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('testResultModal'));
    modal.show();
  }

  // Playground Chat Functionality
  const chatMessages = document.getElementById('chat-messages');
  const chatInput = document.getElementById('chat-input');
  const chatSendBtn = document.getElementById('chat-send-btn');
  const clearChatBtn = document.getElementById('clear-chat-btn');
  const courseSelector = document.getElementById('course-selector');
  const lessonSelector = document.getElementById('lesson-selector');
  const attachmentBtn = document.getElementById('attachment-btn');
  const fileInput = document.getElementById('file-input');
  const chatAttachments = document.getElementById('chat-attachments');
  const attachmentsList = document.getElementById('attachments-list');
  let isGenerating = false;
  let currentSessionId = generateSessionId();
  let playgroundSubscription = null;
  let selectedFiles = [];

  // Generate unique session ID
  function generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Clear cache when entering the page
  function clearPageCache() {
    // Clear localStorage system prompt cache
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key) && key.startsWith("system_prompt")) {
        localStorage.removeItem(key);
      }
    }

    // Clear debug logs cache
    window.debugLogs = [];
    const debugLogsContainer = document.getElementById('debug-logs');
    if (debugLogsContainer) {
      debugLogsContainer.innerHTML = '<div class="text-muted">Function calling logs will appear here...</div>';
    }

    console.log('Page cache cleared');
  }

  // Initialize ActionCable connection for playground
  function initializePlaygroundConnection() {
    if (typeof App !== 'undefined' && App.cable) {
      console.log('Subscribing to playground channel with session_id:', currentSessionId);
      playgroundSubscription = App.cable.subscriptions.create(
        {
          channel: "PlaygroundChatChannel",
          session_id: currentSessionId
        },
        {
          connected() {
            console.log("Playground chat connected with session:", currentSessionId);
          },

          disconnected() {
            console.log("Playground chat disconnected");
          },

          received(data) {
            console.log('ActionCable received data:', data);
            handlePlaygroundMessage(data);
          }
        }
      );
    } else {
      console.error('ActionCable not available');
    }
  }

  function handlePlaygroundMessage(data) {
    console.log('Received playground message:', data);

    if (data.type === 'system_prompt') {
      storeSystemPrompt(data.message_id, data.system_prompt);
    } else if (data.message_id && data.content && data.content !== 'Finished') {
      updateMessage(data.message_id, data.content);
    } else if (data.content === 'Finished') {
      isGenerating = false;
      chatSendBtn.disabled = false;
      chatSendBtn.innerHTML = '<i class="bi bi-send"></i>';

      setTimeout(() => chatInput.focus(), 400);
    } else if (data.type === 'error') {
      const messageId = data.message_id || document.querySelector('.message.assistant:last-child')?.id;
      if (messageId) {
        updateMessage(messageId, `エラー: ${data.message}`);
      }
      showAlert('error', data.message);

      isGenerating = false;
      chatSendBtn.disabled = false;
      chatSendBtn.innerHTML = '<i class="bi bi-send"></i>';
    }
  }

  // Clear cache and initialize connection when page loads
  clearPageCache();
  initializePlaygroundConnection();

  // Clear cache when leaving the page
  window.addEventListener('beforeunload', function() {
    clearPageCache();
  });

  chatInput.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    updateSendButtonState();
  });

  // Function to update send button state
  function updateSendButtonState() {
    const hasText = chatInput.value.trim().length > 0;
    const hasAttachments = selectedFiles.length > 0;

    if (hasText || hasAttachments) {
      chatSendBtn.classList.add('active');
    } else {
      chatSendBtn.classList.remove('active');
    }
  }

  let isComposing = false;

  chatInput.addEventListener('compositionstart', function(e) {
    isComposing = true;
  });

  chatInput.addEventListener('compositionend', function(e) {
    isComposing = false;
  });

  chatInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      sendMessage();
    }
  });

  chatSendBtn.addEventListener('click', sendMessage);

  // File attachment event listeners
  attachmentBtn.addEventListener('click', function() {
    fileInput.click();
  });

  fileInput.addEventListener('change', function(e) {
    const files = Array.from(e.target.files);
    files.forEach(file => addFileAttachment(file));
    e.target.value = ''; // Reset input
  });

  // Drag and drop functionality
  const chatInputArea = document.querySelector('.chat-input-area');

  chatInputArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    e.stopPropagation();
    this.classList.add('drag-over');
  });

  chatInputArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    e.stopPropagation();
    this.classList.remove('drag-over');
  });

  chatInputArea.addEventListener('drop', function(e) {
    e.preventDefault();
    e.stopPropagation();
    this.classList.remove('drag-over');

    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => addFileAttachment(file));
  });

  clearChatBtn.addEventListener('click', function() {
    currentSessionId = generateSessionId();

    // Clear cache when resetting chat
    clearPageCache();

    if (playgroundSubscription) {
      playgroundSubscription.unsubscribe();
    }
    initializePlaygroundConnection();

    chatMessages.innerHTML = `
      <div class="chat-empty-state">
        <i class="bi bi-chat-text"></i>
        <p>プロンプトをテストするために質問を入力してください</p>
        <small>左側でプロンプトを編集すると、ここでリアルタイムでテストできます</small>
      </div>
    `;

    isGenerating = false;
    chatSendBtn.disabled = false;
    chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';
  });

  function sendMessage() {
    const message = chatInput.value.trim();
    if (!message || isGenerating) return;

    const promptTextarea = document.querySelector('textarea[data-prompt-type="lesson"]');
    const promptContent = promptTextarea ? promptTextarea.value : '';

    if (!promptContent.trim()) {
      showAlert('warning', 'プロンプト内容を入力してください');
      return;
    }

    const emptyState = chatMessages.querySelector('.chat-empty-state');
    if (emptyState) {
      chatMessages.innerHTML = '';
    }

    // Add user message with attachments
    addMessage('user', message, selectedFiles);

    chatInput.value = '';
    chatInput.style.height = 'auto';

    // Clear attachments after sending
    const attachmentsToSend = [...selectedFiles];
    selectedFiles = [];
    renderAttachments();

    const tempAssistantId = addMessage('assistant', '');

    isGenerating = true;
    chatSendBtn.disabled = true;
    chatSendBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';

    sendPlaygroundRequest(message, promptContent, tempAssistantId, attachmentsToSend);
  }

  function addMessage(role, content, attachments = []) {
    const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const time = new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' });

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    messageDiv.id = messageId;

    const avatar = role === 'user' ? 'U' : 'AI';
    const avatarClass = role === 'user' ? 'user' : 'assistant';

    // Show typing animation for empty assistant messages
    const messageContent = content || (role === 'assistant' ? `
      <div class="typing-animation">
        <div class="typing-dot dot1"></div>
        <div class="typing-dot dot2"></div>
        <div class="typing-dot dot3"></div>
      </div>
    ` : '');

    // Generate attachments HTML (images only)
    let attachmentsHtml = '';
    if (attachments && attachments.length > 0) {
      attachmentsHtml = '<div class="chat-input-attachments d-flex flex-column gap-1 mb-2">';
      attachments.forEach(file => {
        // Only process images since that's all we allow
        const reader = new FileReader();
        reader.onload = function(e) {
          const imgElement = messageDiv.querySelector(`[data-file-name="${file.name}"]`);
          if (imgElement) {
            imgElement.src = e.target.result;
          }
        };
        reader.readAsDataURL(file);

        attachmentsHtml += `
          <img data-file-name="${file.name}" alt="image" class="message-attachment-image" onclick="openImageModal(this.src)" />
        `;
      });
      attachmentsHtml += '</div>';
    }

    messageDiv.innerHTML = `
      <div class="message-avatar">${avatar}</div>
      <div class="message-bubble">
        <div class="message-text">${messageContent}</div>
        ${attachmentsHtml}
      </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageId;
  }

  function updateMessage(messageId, content) {
    const messageElement = document.getElementById(messageId);
    if (messageElement) {
      const textElement = messageElement.querySelector('.message-text');
      if (textElement) {
        // Remove typing animation if present and replace with content
        const typingAnimation = textElement.querySelector('.typing-animation');
        if (typingAnimation) {
          typingAnimation.remove();
        }

        // If content is empty, show typing animation
        if (!content.trim()) {
          textElement.innerHTML = `
            <div class="typing-animation">
              <div class="typing-dot dot1"></div>
              <div class="typing-dot dot2"></div>
              <div class="typing-dot dot3"></div>
            </div>
          `;
        } else {
          // Process content to handle images
          const processedContent = processMessageContent(content);
          textElement.innerHTML = processedContent;
        }

        chatMessages.scrollTop = chatMessages.scrollHeight;
      } else {
        console.error('Text element not found in message:', messageId);
      }
    } else {
      console.error('Message element not found:', messageId);
    }
  }

  // Function to process message content and convert image URLs to img tags
  function processMessageContent(content) {
    // Regular expression to match image URLs
    const imageRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/gi;

    // Replace image URLs with img tags
    let processedContent = content.replace(imageRegex, (match) => {
      return `<img src="${match}" alt="image" class="message-attachment-image" onclick="openImageModal('${match}')" />`;
    });

    return processedContent;
  }

  // Function to open image in modal
  function openImageModal(imageUrl) {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      cursor: pointer;
    `;

    // Create image element
    const img = document.createElement('img');
    img.src = imageUrl;
    img.style.cssText = `
      max-width: 90%;
      max-height: 90%;
      object-fit: contain;
      border-radius: 8px;
    `;

    modalOverlay.appendChild(img);
    document.body.appendChild(modalOverlay);

    // Close modal when clicking overlay
    modalOverlay.addEventListener('click', () => {
      document.body.removeChild(modalOverlay);
    });

    // Close modal with Escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        document.body.removeChild(modalOverlay);
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);
  }

  // File attachment functions - Match classroom UI (images only)
  function addFileAttachment(file) {
    // Check if file is an image
    if (!/png|jpeg|jpg/.test(file.type)) {
      alert('画像ファイル（PNG、JPG、JPEG）のみアップロード可能です。');
      return;
    }

    // Check file size (max 10MB for images)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      alert('Limit 10 MB per image.');
      return;
    }

    // Check if file already exists
    if (selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
      alert('このファイルは既に選択されています。');
      return;
    }

    selectedFiles.push(file);
    renderAttachments();
  }

  function removeFileAttachment(index) {
    selectedFiles.splice(index, 1);
    renderAttachments();
  }

  function renderAttachments() {
    if (selectedFiles.length === 0) {
      chatAttachments.style.display = 'none';
      updateSendButtonState();
      return;
    }

    chatAttachments.style.display = 'block';
    attachmentsList.innerHTML = '';

    selectedFiles.forEach((file, index) => {
      const attachmentItem = document.createElement('div');
      attachmentItem.className = 'attachment-item';

      const fileType = getFileType(file.name);
      const fileSize = formatFileSize(file.size);

      attachmentItem.innerHTML = `
        <div class="attachment-icon ${fileType}">
          ${getFileIcon(fileType)}
        </div>
        <div class="attachment-info">
          <div class="attachment-name">${file.name}</div>
          <div class="attachment-size">${fileSize}</div>
        </div>
        <button type="button" class="attachment-remove" onclick="removeFileAttachment(${index})">
          <i class="bi bi-x-lg"></i>
        </button>
      `;

      attachmentsList.appendChild(attachmentItem);
    });

    updateSendButtonState();
  }

  function getFileType(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    if (['jpg', 'jpeg', 'png'].includes(ext)) return 'image';
    return 'image'; // Only images are allowed
  }

  function getFileIcon(fileType) {
    return '<i class="material-icons">image</i>';
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Make removeFileAttachment available globally
  window.removeFileAttachment = removeFileAttachment;

  function sendPlaygroundRequest(userMessage, promptContent, assistantMessageId, attachments = []) {
    // Note: File attachments are displayed in UI but not sent to backend yet
    // This would require backend changes to handle file uploads
    const selectedCourseId = courseSelector.value;
    const selectedLessonId = lessonSelector.value;

    fetch('<%= playground_chat_admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent) %>', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({
        message: userMessage,
        prompt_content: promptContent,
        prompt_type: 'lesson',
        session_id: currentSessionId,
        course_id: selectedCourseId || null,
        lesson_id: selectedLessonId || null
      })
    })
    .then(response => {
      return response.json();
    })
    .then(data => {
      if (data.status === 'success') {
        const assistantMessage = document.getElementById(assistantMessageId);
        if (assistantMessage) {
          assistantMessage.id = data.bot_message_id;
        } else {
          console.error('Assistant message not found:', assistantMessageId);
        }
        console.log('Playground chat started:', data);
      } else {
        updateMessage(assistantMessageId, `エラー: ${data.message}`);
        showAlert('error', data.message);

        isGenerating = false;
        chatSendBtn.disabled = false;
        chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';
      }
    })
    .catch(error => {
      console.error('Playground chat error:', error);
      updateMessage(assistantMessageId, 'エラーが発生しました。もう一度お試しください。');
      showAlert('error', 'チャット処理中にエラーが発生しました');

      isGenerating = false;
      chatSendBtn.disabled = false;
      chatSendBtn.innerHTML = '<i class="bi bi-send"></i> 送信';
    });
  }

  courseSelector.addEventListener('change', function() {
    const courseId = this.value;

    lessonSelector.innerHTML = '<option value="">レッスンを読み込み中...</option>';
    lessonSelector.disabled = true;

    if (courseId) {
      fetch(`/admin/schools/<%= @school.id %>/courses/${courseId}/course_lessons.json`)
        .then(response => response.json())
        .then(lessons => {
          lessonSelector.innerHTML = '<option value="">レッスンを選択してください...</option>';

          lessons.forEach(lesson => {
            const option = document.createElement('option');
            option.value = lesson.id;
            option.textContent = lesson.name;
            lessonSelector.appendChild(option);
          });

          lessonSelector.disabled = false;
        })
        .catch(error => {
          console.error('Error loading lessons:', error);
          lessonSelector.innerHTML = '<option value="">レッスンの読み込みに失敗しました</option>';
        });
    } else {
      lessonSelector.innerHTML = '<option value="">まずコースを選択してください...</option>';
      lessonSelector.disabled = true;
    }
  });

  window.debugLogs = [];
  window.autoScrollEnabled = true; // Default: enabled

  // Toggle auto-scroll functionality
  window.toggleAutoScroll = function() {
    window.autoScrollEnabled = !window.autoScrollEnabled;
    const toggleBtn = document.getElementById('auto-scroll-toggle');

    if (window.autoScrollEnabled) {
      toggleBtn.innerHTML = '<i class="bi bi-arrow-down-circle-fill"></i> Auto Scroll: ON';
      toggleBtn.className = 'btn btn-sm btn-outline-success me-2';

      // Scroll to bottom when enabling
      const debugLogsContainer = document.getElementById('debug-logs');
      if (debugLogsContainer) {
        debugLogsContainer.scrollTop = debugLogsContainer.scrollHeight;
      }
    } else {
      toggleBtn.innerHTML = '<i class="bi bi-arrow-down-circle"></i> Auto Scroll: OFF';
      toggleBtn.className = 'btn btn-sm btn-outline-secondary me-2';
    }
  };

  // Initialize auto-scroll button state
  document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('auto-scroll-toggle');
    if (toggleBtn && window.autoScrollEnabled) {
      toggleBtn.innerHTML = '<i class="bi bi-arrow-down-circle-fill"></i> Auto Scroll: ON';
      toggleBtn.className = 'btn btn-sm btn-outline-success me-2';
    }
  });

  document.getElementById('debug-tab').addEventListener('shown.bs.tab', function() {
    loadDebugInfo(true);
  });

  window.clearDebugLogs = function() {
    fetch(`/admin/schools/<%= @school.id %>/agents/<%= @ai_tutor_agent.id %>/prompts/clear_debug`, {
      method: 'DELETE',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    }).then(() => {
      window.debugLogs = [];
      const debugLogsContainer = document.getElementById('debug-logs');
      debugLogsContainer.innerHTML = '<div class="text-muted">Function calling logs will appear here...</div>';
    }).catch(error => {
      console.error('Failed to clear server debug logs:', error);
      window.debugLogs = [];
      const debugLogsContainer = document.getElementById('debug-logs');
      debugLogsContainer.innerHTML = '<div class="text-muted">Function calling logs will appear here...</div>';
    });
  };

  window.addDebugLog = function(type, message, data = null) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      timestamp,
      type,
      message,
      data
    };

    window.debugLogs.push(logEntry);

    const debugLogsContainer = document.getElementById('debug-logs');
    if (!debugLogsContainer) return;

    const placeholderElement = debugLogsContainer.querySelector('.text-muted');
    const isActualPlaceholder = placeholderElement &&
      (placeholderElement.textContent.includes('Function calling logs will appear here') ||
       placeholderElement.textContent.includes('No debug logs available'));

    if (isActualPlaceholder) {
      debugLogsContainer.innerHTML = '';
    }

    const logElement = document.createElement('div');
    logElement.className = 'debug-log-entry mb-2 p-2 border-start border-3';

    let borderColor = 'border-secondary';
    let icon = 'bi-info-circle';

    switch(type) {
      case 'tool_added':
        borderColor = 'border-primary';
        icon = 'bi-wrench';
        break;
      case 'tool_created':
        borderColor = 'border-success';
        icon = 'bi-gear';
        break;
      case 'tool_executed':
        borderColor = 'border-warning';
        icon = 'bi-play-circle';
        break;
      case 'tool_result':
        borderColor = 'border-info';
        icon = 'bi-check-circle';
        break;
      case 'system_prompt':
        borderColor = 'border-info';
        icon = 'bi-file-text';
        break;
      case 'user_input':
        borderColor = 'border-primary';
        icon = 'bi-chat-quote';
        break;
      case 'error':
        borderColor = 'border-danger';
        icon = 'bi-exclamation-triangle';
        break;
      default:
        borderColor = 'border-secondary';
        icon = 'bi-info-circle';
    }

    logElement.classList.add(borderColor);

    let logHtml = `
      <div class="d-flex align-items-start">
        <i class="bi ${icon} me-2 mt-1"></i>
        <div class="flex-grow-1">
          <div class="d-flex justify-content-between">
            <strong class="text-${type === 'error' ? 'danger' : 'dark'}">${message}</strong>
            <small class="text-muted">${timestamp}</small>
          </div>
    `;



    if (data) {
      logHtml += `
          <div class="mt-1">
            <small class="text-muted">Data:</small>
            <div class="mt-1">
              <pre class="mb-0 bg-light p-2 rounded" style="font-size: 0.7rem; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>
            </div>
          </div>
      `;
    }

    logHtml += `
        </div>
      </div>
    `;

    logElement.innerHTML = logHtml;
    debugLogsContainer.appendChild(logElement);

    // Auto scroll to bottom only if auto-scroll is enabled
    if (window.autoScrollEnabled) {
      debugLogsContainer.scrollTop = debugLogsContainer.scrollHeight;
    }
  };

  window.loadDebugInfo = function(forceRefresh = false) {
    fetch(`/admin/schools/<%= @school.id %>/agents/<%= @ai_tutor_agent.id %>/prompts/debug_info`)
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          const debugLogsContainer = document.getElementById('debug-logs');

          window.debugLogs = [];
          debugLogsContainer.innerHTML = '';

          data.debug_logs.forEach(log => {
            window.addDebugLog(log.type, log.message, log.data);
          });

          if (window.debugLogs.length === 0) {
            debugLogsContainer.innerHTML = '<div class="text-muted">No debug logs available</div>';
          } else if (forceRefresh && window.autoScrollEnabled) {
            // Only scroll to bottom on force refresh if auto-scroll is enabled
            debugLogsContainer.scrollTop = debugLogsContainer.scrollHeight;
          }
        }
      })
      .catch(error => {
        console.error('Failed to load debug info:', error);
      });
  };

  // Auto-refresh debug info every 5 seconds when debug tab is active
  setInterval(() => {
    const debugTab = document.getElementById('debug-panel-tab');
    if (debugTab && debugTab.classList.contains('active')) {
      loadDebugInfo();
    }
  }, 5000);
});
</script>
