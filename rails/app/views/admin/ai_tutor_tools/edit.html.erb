<div class="tool-form-container">
  <div class="form-card">

    <%= form_with model: [:admin, @school, @ai_tutor_agent, @ai_tutor_tool], local: true, html: { class: 'tool-form' } do |form| %>
      <% if @ai_tutor_tool.errors.any? %>
        <div class="alert alert-danger">
          <h6><i class="bi bi-exclamation-triangle me-1"></i>エラーが発生しました:</h6>
          <ul class="mb-0">
            <% @ai_tutor_tool.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <% if @ai_tutor_tool.is_default? %>
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i>
          これはシステム標準ツールです。一部の設定のみ変更可能です。
        </div>
      <% end %>

      <div class="form-group">
        <%= form.label :tool_name, "ツール名", class: "form-label" %>
        <%= form.text_field :tool_name,
            class: "form-control #{'readonly-field' if @ai_tutor_tool.is_default?}",
            placeholder: "例: search_learning_history",
            readonly: @ai_tutor_tool.is_default? %>
        <div class="help-text">
          <% if @ai_tutor_tool.is_default? %>
            システム標準ツールの名前は変更できません
          <% else %>
            英数字とアンダースコアのみ使用可能
          <% end %>
        </div>
      </div>

      <div class="form-group">
        <%= form.label :tool_type, "ツールタイプ", class: "form-label" %>
        <%= form.select :tool_type,
            options_for_select([
              ['検索 (search)', 'search'],
              ['分析 (analysis)', 'analysis'],
              ['進捗 (progress)', 'progress'],
              ['RAG (rag)', 'rag'],
              ['情報 (info)', 'info']
            ], @ai_tutor_tool.tool_type),
            { prompt: 'タイプを選択してください' },
            { class: "form-select", disabled: @ai_tutor_tool.is_default? } %>
      </div>

      <div class="form-group">
        <%= form.label :description, "説明", class: "form-label" %>
        <%= form.text_area :description, class: "form-control", rows: 3, placeholder: "このツールの機能を説明してください", readonly: @ai_tutor_tool.is_default? %>
      </div>

      <div class="form-group">
        <%= form.label :when_to_use, "When to use", class: "form-label" %>
        <%= form.text_area :when_to_use, class: "form-control", rows: 3, placeholder: "このツールをいつ、どのような場面で使用するかを説明してください" %>
        <div class="help-text">例：生徒が過去の学習履歴を確認したい時、特定の期間の学習状況を調べたい時に使用します</div>
      </div>

      <div class="form-group">
        <%= form.label :function_definition, "Function Definition", class: "form-label" %>
        <%= form.text_area :function_definition,
            class: "form-control code-editor #{'readonly-field' if @ai_tutor_tool.is_default?}",
            placeholder: "def your_function_name(params)
  # ここに実行するRubyコードを記述
  # 例:
  # user = User.find(user_id)
  # user.some_method
end", readonly: @ai_tutor_tool.is_default? %>
        <div class="help-text">
          <% if @ai_tutor_tool.is_default? %>
            システム標準ツールのコードは変更できません
          <% else %>
            実行可能なRubyコードを記述してください
          <% end %>
        </div>

        <details class="mt-2">
          <summary class="text-muted small" style="cursor: pointer;">サンプルコード例</summary>
          <div class="example-code">
def search_learning_history(user_id, subject: nil, days: 30)
  user = User.find(user_id)
  query = user.learning_histories.where("created_at >= ?", days.days.ago)
  query = query.where(subject: subject) if subject.present?

  query.order(created_at: :desc).limit(50).map do |history|
    {
      date: history.created_at.strftime("%Y-%m-%d"),
      subject: history.subject,
      lesson: history.lesson_title,
      score: history.score
    }
  end
end
          </div>
        </details>
      </div>

      <div class="form-group">
        <div class="form-check">
          <%= form.check_box :enabled, class: "form-check-input" %>
          <%= form.label :enabled, "有効にする", class: "form-check-label" %>
        </div>
      </div>

      <div class="btn-group">
        <%= link_to admin_school_ai_tutor_agent_ai_tutor_tools_path(@school, @ai_tutor_agent), 
                     class: "btn btn-outline-secondary" do %>
          <i class="bi bi-arrow-left me-1"></i>キャンセル
        <% end %>
        <%= form.submit "ツールを更新", class: "btn btn-primary" %>
      </div>
    <% end %>
  </div>
</div>

<style>
.tool-form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.help-text {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.code-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  min-height: 200px;
}

.example-code {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 0.5rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
}

.btn-group {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.readonly-field {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
  opacity: 0.8;
}

.readonly-field:focus {
  background-color: #f8f9fa !important;
  border-color: #ced4da !important;
  box-shadow: none !important;
}
</style>
