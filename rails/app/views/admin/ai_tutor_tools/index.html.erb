<% content_for :title, "AIエージェント管理システム - ツール管理 - #{@school.name}" %>

<%= render 'admin/shared/ai_tutor_styles' %>

<style>
  .section-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 2rem;
  }

  /* Section Header */
  .section-header {
    background: #f7fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .section-icon {
    width: 40px;
    height: 40px;
    background: var(--accent-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
  }

  .section-info h3.section-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    text-transform: capitalize;
  }

  .section-subtitle {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.85rem;
  }

  .section-stats {
    margin-left: auto;
  }

  .tool-count-badge {
    background: #e2e8f0;
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.8rem;
  }

  /* Section Body */
  .section-body {
    padding: 1rem 1.5rem;
  }

  .tools-row {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* Tool Items */
  .tool-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 1.25rem;
    transition: all 0.2s ease;
  }

  .tool-item:hover {
    background: var(--bg-secondary);
    border-color: var(--accent-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .tool-item-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    gap: 1rem;
  }

  .tool-status-info {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    flex: 1;
  }

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-top: 0.25rem;
    flex-shrink: 0;
  }

  .status-active {
    background: var(--success-color);
  }

  .status-inactive {
    background: var(--text-secondary);
  }

  .tool-name-wrapper {
    flex: 1;
  }

  .tool-name {
    font-weight: 600;
    font-size: 1rem;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
  }

  .default-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: var(--accent-color);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
  }

  .tool-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
  }

  .tool-type-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    background: #e2e8f0;
    color: var(--text-secondary);
  }

  .tool-description {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .tool-when-to-use {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
    background: #fef3c7;
    border: 1px solid #fbbf24;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .tool-when-to-use strong {
    color: #92400e;
  }

  .when-to-use-edit textarea {
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.85rem;
    resize: vertical;
    min-height: 60px;
  }

  .when-to-use-edit textarea:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    outline: none;
  }

  /* Function Details */
  .function-details {
    margin-top: 0.75rem;
  }

  .function-summary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #f1f5f9;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    color: var(--accent-color);
    font-size: 0.85rem;
  }

  .function-summary:hover {
    background: #e2e8f0;
  }

  .function-summary i.bi-chevron-down {
    margin-left: auto;
    transition: transform 0.2s ease;
  }

  .function-details[open] .function-summary i.bi-chevron-down {
    transform: rotate(180deg);
  }

  .function-content {
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 6px 6px;
    padding: 0.75rem;
    background: #f8fafc;
  }

  .function-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #e6fffa;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;
    color: #234e52;
  }

  .code-block {
    position: relative;
    background: #2d3748;
    border-radius: 4px;
    overflow: hidden;
  }

  .code-block pre {
    margin: 0;
    padding: 1rem;
    background: transparent;
    color: #e2e8f0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
    overflow-x: auto;
  }

  .copy-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
  }

  .copy-btn:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
  }

  /* Tool Actions */
  .tool-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.8rem;
    text-decoration: none;
    border: 1px solid;
  }

  .edit-btn {
    background: transparent;
    border-color: var(--accent-color);
    color: var(--accent-color);
  }

  .edit-btn:hover {
    background: var(--accent-color);
    color: white;
    text-decoration: none;
  }

  .delete-btn {
    background: transparent;
    border-color: var(--danger-color);
    color: var(--danger-color);
  }

  .delete-btn:hover {
    background: var(--danger-color);
    color: white;
    text-decoration: none;
  }

  .tool-system-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0.5rem 0.75rem;
    background: #f1f5f9;
    border-radius: 4px;
    color: var(--text-secondary);
    font-size: 0.8rem;
  }

  /* Form Controls - Keep Bootstrap defaults */
  .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .content-area {
      padding: 1rem;
    }
    .top-nav {
      padding: 1rem;
    }
    .section-header {
      padding: 1rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    .section-title-wrapper {
      gap: 0.75rem;
    }
    .section-icon {
      width: 35px;
      height: 35px;
      font-size: 1rem;
    }
    .section-body {
      padding: 1rem;
    }
    .tool-item {
      padding: 1rem;
    }
  }

  @media (max-width: 576px) {
    .section-title-wrapper {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    .tool-item-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }
    .tool-controls {
      align-self: stretch;
      justify-content: space-between;
    }
    .tool-actions {
      flex-direction: column;
    }
  }
</style>

<div class="main-container">
  <%= render 'admin/shared/ai_tutor_agent_detail_navigation', current_page: 'tools' %>

  <!-- Content Area -->
  <div class="content-area">
    <div class="tab-content" id="mainTabsContent">
      <!-- ツール管理 -->
      <div class="tab-pane fade show active" id="tools" role="tabpanel">
        <% @tool_types.each do |tool_type| %>
          <% tools_in_type = @tools_by_type[tool_type] || [] %>
          <% next if tools_in_type.empty? %>

          <!-- Tool Type Section Card -->
          <div class="section-card">
            <!-- Section Header -->
            <div class="section-header">
              <div class="section-title-wrapper">
                <div class="section-icon">
                  <i class="bi bi-<%= tool_type == 'search' ? 'search' :
                                    tool_type == 'analysis' ? 'graph-up' :
                                    tool_type == 'progress' ? 'bar-chart' : 'tools' %>"></i>
                </div>
                <div class="section-info">
                  <h3 class="section-title"><%= tool_type.humanize %></h3>
                  <p class="section-subtitle">
                    <%= case tool_type
                        when 'search' then '学習履歴の検索と分析ツール'
                        when 'analysis' then '学習データの分析と評価ツール'
                        when 'progress' then '学習進捗の追跡と統計ツール'
                        else 'その他のツール'
                        end %>
                  </p>
                </div>
              </div>
              <div class="section-stats">
                <span class="tool-count-badge">
                  <i class="bi bi-collection me-1"></i>
                  <%= tools_in_type.count %>
                </span>
              </div>
            </div>

            <!-- Section Body -->
            <div class="section-body">
              <div class="tools-row">
              <% tools_in_type.each do |tool| %>
                <div class="tool-item">
                  <div class="tool-header">
                    <div class="d-flex align-items-center flex-grow-1">
                      <div class="tool-name-container">
                        <span class="tool-name">
                          <%= tool.tool_name %>
                        </span>
                        <div class="form-check form-switch">
                          <input class="form-check-input tool-toggle"
                                type="checkbox"
                                data-tool-id="<%= tool.id %>"
                                <%= 'checked' if tool.enabled %>>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="tool-description">
                    <i class="bi bi-info-circle text-primary me-2"></i>
                    <%= tool.description %>
                  </div>

                  <% if tool.respond_to?(:when_to_use) && tool.when_to_use.present? %>
                    <div class="tool-when-to-use mt-2">
                      <i class="bi bi-lightbulb text-warning me-2"></i>
                      <strong>When to use:</strong>
                      <div class="d-flex align-items-start gap-2 mt-2">
                        <div class="flex-grow-1">
                          <div class="when-to-use-display" id="when-to-use-display-<%= tool.id %>">
                            <%= tool.when_to_use %>
                          </div>
                          <div class="when-to-use-edit d-none" id="when-to-use-edit-<%= tool.id %>">
                            <textarea class="form-control" 
                                      id="when-to-use-textarea-<%= tool.id %>"
                                      rows="3"
                                      maxlength="1000"><%= tool.when_to_use %></textarea>
                            <div class="mt-2">
                              <button class="btn btn-sm btn-success me-2" 
                                      onclick="saveWhenToUse('<%= tool.id %>')">
                                <i class="bi bi-check"></i> 保存
                              </button>
                              <button class="btn btn-sm btn-secondary" 
                                      onclick="cancelEditWhenToUse('<%= tool.id %>')">
                                <i class="bi bi-x"></i> キャンセル
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% end %>

                  <% if tool.function_definition.present? %>
                    <details class="mt-3">
                      <summary class="text-primary fw-bold small d-flex align-items-center" style="cursor: pointer;">
                        <i class="bi bi-code-slash me-2"></i>
                        Function Definition
                        <i class="bi bi-chevron-down ms-auto"></i>
                      </summary>
                      <div class="function-definition mt-3">
                        <div class="position-relative">
                          <pre class="mb-0"><code class="language-ruby"><%= tool.function_definition %></code></pre>
                          <button class="btn btn-sm btn-outline-secondary position-absolute top-0 end-0 m-2"
                                  onclick="navigator.clipboard.writeText(this.parentElement.querySelector('code').textContent)"
                                  title="コードをコピー">
                            <i class="bi bi-clipboard"></i>
                          </button>
                        </div>
                      </div>
                    </details>
                  <% end %>

                  <div class="mt-4 pt-3 border-top d-flex gap-2 justify-content-end">
                    <%= link_to edit_admin_school_ai_tutor_agent_ai_tutor_tool_path(@school, @ai_tutor_agent, tool.id),
                                class: "btn btn-sm btn-outline-primary d-flex align-items-center" do %>
                      <i class="bi bi-pencil me-2"></i>編集
                    <% end %>
                    <% unless tool.is_default %>
                      <%= link_to admin_school_ai_tutor_agent_ai_tutor_tool_path(@school, @ai_tutor_agent, tool.id),
                                  method: :delete,
                                  class: "btn btn-sm btn-outline-danger d-flex align-items-center",
                                  confirm: "本当に削除しますか？" do %>
                        <i class="bi bi-trash me-2"></i>削除
                      <% end %>
                    <% end %>
                  </div>
                </div>
              <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Add loading animation to tool items
  const toolItems = document.querySelectorAll('.tool-item');
  toolItems.forEach((item, index) => {
    item.style.opacity = '0';
    item.style.transform = 'translateY(30px)';
    setTimeout(() => {
      item.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
      item.style.opacity = '1';
      item.style.transform = 'translateY(0)';
    }, index * 100);
  });

  // Handle tool toggle switches with enhanced feedback
  document.querySelectorAll('.tool-toggle').forEach(toggle => {
    toggle.addEventListener('change', function() {
      const toolId = this.dataset.toolId;
      const enabled = this.checked;
      const toolItem = this.closest('.tool-item');

      // Add loading state
      this.disabled = true;
      toolItem.style.opacity = '0.7';

      fetch(`/admin/schools/<%= @school.id %>/agents/<%= @ai_tutor_agent.id %>/tools/${toolId}/toggle_enabled`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ enabled: enabled })
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          // Show success feedback with toast
          showToast('success', data.message);

          // Add visual feedback to the tool item
          if (data.enabled) {
            toolItem.style.borderLeft = '4px solid #28a745';
          } else {
            toolItem.style.borderLeft = '4px solid #dc3545';
          }

          // Reset border after 2 seconds
          setTimeout(() => {
            toolItem.style.borderLeft = '';
          }, 2000);
        } else {
          // Revert toggle if failed
          this.checked = !enabled;
          showToast('error', data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        // Revert toggle if failed
        this.checked = !enabled;
        showToast('error', 'エラーが発生しました');
      })
      .finally(() => {
        // Remove loading state
        this.disabled = false;
        toolItem.style.opacity = '1';
      });
    });
  });

  // Enhanced toast notification system
  function showToast(type, message) {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.custom-toast');
    existingToasts.forEach(toast => toast.remove());

    const toastContainer = document.createElement('div');
    toastContainer.className = 'custom-toast';
    toastContainer.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      min-width: 300px;
      background: ${type === 'success' ? 'linear-gradient(135deg, #48bb78, #38a169)' : 'linear-gradient(135deg, #f56565, #e53e3e)'};
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
      transform: translateX(100%);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(20px);
    `;

    toastContainer.innerHTML = `
      <div class="d-flex align-items-center">
        <i class="bi bi-${type === 'success' ? 'check-circle-fill' : 'x-circle-fill'} me-3" style="font-size: 1.2rem;"></i>
        <span style="font-weight: 500;">${message}</span>
        <button onclick="this.parentElement.parentElement.remove()"
                style="background: none; border: none; color: white; margin-left: auto; padding: 0; font-size: 1.2rem;">
          <i class="bi bi-x"></i>
        </button>
      </div>
    `;

    document.body.appendChild(toastContainer);

    // Animate in
    setTimeout(() => {
      toastContainer.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove after 4 seconds
    setTimeout(() => {
      toastContainer.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (toastContainer.parentNode) {
          toastContainer.remove();
        }
      }, 400);
    }, 4000);
  }

  // Add smooth scrolling to section headers
  document.querySelectorAll('.section-header').forEach(header => {
    header.addEventListener('click', function() {
      this.scrollIntoView({ behavior: 'smooth', block: 'start' });
    });
  });

  // Add copy functionality enhancement
  document.querySelectorAll('button[onclick*="clipboard"]').forEach(btn => {
    btn.addEventListener('click', function() {
      const icon = this.querySelector('i');
      const originalClass = icon.className;

      // Change icon to check
      icon.className = 'bi bi-check';
      this.style.background = 'var(--success-color)';
      this.style.borderColor = 'var(--success-color)';

      // Reset after 2 seconds
      setTimeout(() => {
        icon.className = originalClass;
        this.style.background = '';
        this.style.borderColor = '';
      }, 2000);

      showToast('success', 'コードがクリップボードにコピーされました');
    });
  });

  window.editWhenToUse = function(toolId) {
    document.getElementById(`when-to-use-display-${toolId}`).classList.add('d-none');
    document.getElementById(`when-to-use-edit-${toolId}`).classList.remove('d-none');
    document.getElementById(`edit-when-to-use-btn-${toolId}`).style.display = 'none';
  };

  window.cancelEditWhenToUse = function(toolId) {
    const originalText = document.getElementById(`when-to-use-display-${toolId}`).textContent.trim();
    document.getElementById(`when-to-use-textarea-${toolId}`).value = originalText;
    document.getElementById(`when-to-use-display-${toolId}`).classList.remove('d-none');
    document.getElementById(`when-to-use-edit-${toolId}`).classList.add('d-none');
    document.getElementById(`edit-when-to-use-btn-${toolId}`).style.display = 'block';
  };

  window.saveWhenToUse = function(toolId) {
    const textarea = document.getElementById(`when-to-use-textarea-${toolId}`);
    const newText = textarea.value.trim();
    
    if (!newText) {
      showToast('error', '使用場面を入力してください');
      return;
    }

    const saveBtn = event.target;
    const originalContent = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="bi bi-spinner-border animate-spin"></i> 保存中...';
    saveBtn.disabled = true;

    fetch(`/admin/schools/<%= @school.id %>/agents/<%= @ai_tutor_agent.id %>/tools/${toolId}/update_when_to_use`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ when_to_use: newText })
    })
    .then(response => response.json())
    .then(data => {
      if (data.status === 'success') {
        document.getElementById(`when-to-use-display-${toolId}`).textContent = newText;
        
        document.getElementById(`when-to-use-display-${toolId}`).classList.remove('d-none');
        document.getElementById(`when-to-use-edit-${toolId}`).classList.add('d-none');
        document.getElementById(`edit-when-to-use-btn-${toolId}`).style.display = 'block';
        
        showToast('success', 'when_to_use が正常に更新されました');
      } else {
        showToast('error', data.message || 'エラーが発生しました');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      showToast('error', 'エラーが発生しました');
    })
    .finally(() => {
      saveBtn.innerHTML = originalContent;
      saveBtn.disabled = false;
    });
  };
});
</script>
