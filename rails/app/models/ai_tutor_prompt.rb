class AiTutorPrompt < ApplicationRecord
  belongs_to :ai_tutor_agent
  belongs_to :school

  PROMPT_TYPES = %w[lesson test dashboard goal exam].freeze

  validates :prompt_type, presence: true, inclusion: { in: PROMPT_TYPES }
  validates :content, presence: true
  validates :enabled, inclusion: { in: [true, false] }
  validates :prompt_type, uniqueness: { scope: :ai_tutor_agent_id }

  scope :enabled, -> { where(enabled: true) }
  scope :by_type, ->(type) { where(prompt_type: type) }

  def self.system_prompt_for(agent, prompt_type)
    agent.ai_tutor_prompts.enabled.find_by(prompt_type: prompt_type)&.content
  end

  def self.system_prompt_for_school(school, prompt_type)
    agent = school.ai_tutor_agents.find_by(agent_type: prompt_type)
    return nil unless agent

    system_prompt_for(agent, prompt_type)
  end

  def render_content(variables = {})
    content_with_variables = content.dup

    variables.each do |key, value|
      placeholder = "{{#{key}}}"
      content_with_variables.gsub!(placeholder, value.to_s)
    end

    content_with_variables
  end

  def available_variables
    case prompt_type
    when 'lesson'
      %w[lesson_id lesson_title lesson_body materials user_name]
    when 'test'
      %w[test_id test_title test_description questions user_name]
    when 'dashboard'
      %w[user_name user_profile recent_activities user_input]
    when 'goal'
      %w[goal_id goal_title goal_description progress user_input user_name]
    when 'exam'
      %w[exam_id exam_title exam_description questions user_input user_name]
    else
      %w[user_name]
    end
  end

  def available_variables_with_descriptions
    case prompt_type
    when 'lesson'
      {
        'lesson_id' => 'レッスンのID (例: "123")',
        'lesson_title' => 'レッスンのタイトル (例: "英語の過去形")',
        'lesson_body' => 'レッスンの本文・説明 (例: "過去形の基本的な使い方を学習します")',
        'materials' => 'レッスンに関連する教材リスト (例: "教材1: 過去形ルール\\n教材2: 不規則動詞")',
        'user_name' => 'ユーザーの名前 (例: "田中太郎")'
      }
    when 'test'
      {
        'test_id' => 'テストのID (例: "123")',
        'test_title' => 'テストのタイトル (例: "数学基礎テスト")',
        'test_description' => 'テストの説明 (例: "基本的な計算問題")',
        'questions' => 'テストの問題リスト (例: "問1: 2 + 3 = ?\\n問2: 5 × 4 = ?")',
        'user_name' => 'ユーザーの名前 (例: "田中太郎")'
      }
    when 'dashboard'
      {
        'user_name' => 'ユーザーの名前 (例: "田中太郎")',
        'user_profile' => 'ユーザーのプロフィール情報 (例: "名前: 田中太郎\\n学年: 中学2年")',
        'recent_activities' => '最近の学習活動 (例: "2024/01/15: レッスン完了\\n2024/01/14: テスト受験")'
      }
    when 'goal'
      {
        'goal_id' => '学習目標のID (例: "123")',
        'goal_title' => '学習目標のタイトル (例: "英語の過去形を理解する")',
        'goal_description' => '学習目標の説明 (例: "過去形の基本的な使い方を学習します")',
        'progress' => '学習目標の進捗状況 (例: "50%")',
        'user_name' => 'ユーザーの名前 (例: "田中太郎")'
      }
    when 'exam'
      {
        'exam_id' => '試験のID (例: "123")',
        'exam_title' => '試験のタイトル (例: "英語試験")',
        'exam_description' => '試験の説明 (例: "過去形の基本的な使い方を学習します")',
        'questions' => '試験の問題リスト (例: "問1: 2 + 3 = ?\\n問2: 5 × 4 = ?")',
        'user_name' => 'ユーザーの名前 (例: "田中太郎")'
      }
    else
      {
        'user_name' => 'ユーザーの名前'
      }
    end
  end

  def self.default_content_for(prompt_type)
    case prompt_type
    when 'lesson'
      <<~PROMPT
        レッスン中の質問に対して段階的に説明してください。
        現在のレッスン：{{lesson_title}}

        【関連教材】{{materials}}
        【生徒の質問】{{user_input}}

        生徒の理解度に合わせて、分かりやすく丁寧に説明してください。
      PROMPT
    when 'test'
      <<~PROMPT
        テスト中の質問に対してヒントを提供してください。
        現在のテスト：{{test_title}}

        【テスト内容】{{test_description}}
        【生徒の質問】{{user_input}}

        答えを直接教えるのではなく、考え方のヒントを提供してください。
      PROMPT
    when 'dashboard'
      <<~PROMPT
        あなたは中学生向けの優秀なAIチューターです。
        以下の情報を参考にして適切な指導を行ってください：

        【生徒情報】{{user_profile}}
        【最近の活動】{{recent_activities}}
        【生徒の質問】{{user_input}}
      PROMPT
    when 'goal'
      <<~PROMPT
        学習目標に関する質問にお答えします。
        現在の目標：{{goal_title}}

        【目標の説明】{{goal_description}}
        【進捗状況】{{progress}}
        【生徒の質問】{{user_input}}

        目標達成に向けた具体的なアドバイスを提供してください。
      PROMPT
    when 'exam'
      <<~PROMPT
        試験に関する質問にお答えします。
        試験名：{{exam_title}}

        【試験内容】{{exam_description}}
        【生徒の質問】{{user_input}}

        試験対策として効果的なアドバイスを提供してください。
      PROMPT
    else
      <<~PROMPT
        あなたは優秀なAIチューターです。
        生徒の質問に対して適切な指導を行ってください。

        【生徒の質問】{{user_input}}
      PROMPT
    end
  end
end
