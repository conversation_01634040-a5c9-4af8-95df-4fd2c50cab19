class AiTutorTool < ApplicationRecord
  belongs_to :ai_tutor_agent
  belongs_to :school

  TOOL_TYPES = %w[search analysis progress rag info].freeze

  validates :tool_name, presence: true, uniqueness: { scope: :ai_tutor_agent_id }
  validates :tool_type, presence: true, inclusion: { in: TOOL_TYPES }
  validates :enabled, inclusion: { in: [true, false] }
  validates :when_to_use, length: { maximum: 1000 }, allow_blank: true
  validate :tool_name_not_default, unless: :is_default?

  def is_default?
    is_default == true
  end

  def is_default_tool_override?
    is_default? && function_definition.blank?
  end

  private

  def tool_name_not_default
    if self.class.conflicts_with_default?(tool_name)
      errors.add(:tool_name, 'はデフォルトツールと重複しています')
    end
  end

  scope :enabled, -> { where(enabled: true) }
  scope :by_type, ->(type) { where(tool_type: type) }
  scope :default_tools, -> { where(is_default: true) }
  scope :custom_tools, -> { where(is_default: false) }

  def function_definition_hash
    return {} if function_definition.blank?

    JSON.parse(function_definition)
  rescue JSON::ParserError
    {}
  end

  def configuration_hash
    return {} if configuration.blank?

    JSON.parse(configuration)
  rescue JSON::ParserError
    {}
  end

  def function_definition_hash=(hash)
    self.function_definition = hash.to_json
  end

  def configuration_hash=(hash)
    self.configuration = hash.to_json
  end

  def self.conflicts_with_default?(tool_name)
    FunctionCalling.default_tools.any? { |tool| tool[:tool_name] == tool_name }
  end

  def self.all_tools_for_agent(agent)
    sync_default_tools_for_agent(agent)

    agent.ai_tutor_tools.includes(:ai_tutor_agent, :school).map do |tool|
      OpenStruct.new(
        id: tool.id,
        tool_name: tool.tool_name,
        tool_type: tool.tool_type,
        description: tool.description,
        when_to_use: tool.when_to_use,
        function_definition: tool.function_definition,
        enabled: tool.enabled,
        ai_tutor_agent_id: tool.ai_tutor_agent_id,
        school_id: tool.school_id,
        is_default: tool.is_default?
      )
    end
  end

  def self.all_tools_for_school(school)
    all_tools = []

    school.ai_tutor_agents.each do |agent|
      all_tools += all_tools_for_agent(agent)
    end

    all_tools
  end

  def self.sync_default_tools_for_agent(agent)
    FunctionCalling.default_tools.each do |default_tool|
      existing_tool = agent.ai_tutor_tools.find_by(tool_name: default_tool[:tool_name])

      if existing_tool
        unless existing_tool.is_default_tool_override?
          existing_tool.update!(
            tool_type: default_tool[:tool_type],
            description: default_tool[:description],
            function_definition: default_tool[:function_definition],
            is_default: true
          )
        end
      else
        agent.ai_tutor_tools.create!(
          school: agent.school,
          tool_name: default_tool[:tool_name],
          tool_type: default_tool[:tool_type],
          description: default_tool[:description],
          when_to_use: default_tool[:when_to_use],
          function_definition: default_tool[:function_definition],
          enabled: true,
          is_default: true
        )
      end
    end
  end
end
