module AiTutor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  extend self

  def build_system_message(ai_tutor_prompt, object, messages)
    return ai_tutor_prompt.content if ai_tutor_prompt.content.blank?

    PromptVariables.initialize_context(object, messages)
    used_variables = detect_variables_in_prompt(ai_tutor_prompt.content)

    return ai_tutor_prompt.content if used_variables.empty?

    variables = build_variables(used_variables)
    ai_tutor_prompt.render_content(variables)
  end

  private

  def detect_variables_in_prompt(content)
    content.scan(/\{\{([^}]+)\}\}/).flatten.map(&:strip)
  end

  def build_variables(used_variables)
    variables = {}

    used_variables.each do |variable|
      if PromptVariables.respond_to?(variable)
        variables[variable.to_sym] = PromptVariables.send(variable)
      else
        Rails.logger.warn "Unknown variable in AI Tutor Prompt: #{variable}"
        variables[variable.to_sym] = ''
      end
    end

    variables
  end
end
