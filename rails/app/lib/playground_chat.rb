module PlaygroundChat
  extend self

  def self.add_debug_info(session, type, message, data = nil, user_id = nil)
    user_id = user_id || Thread.current[:current_user]&.id || Thread.current[:debug_user_id] || 'anonymous'
    cache_key = "playground_debug_#{user_id}"
    log_entry = {
      id: SecureRandom.hex(8),
      timestamp: Time.current,
      type: type,
      message: message,
      data: data
    }

    begin
      cached_logs = Rails.cache.read(cache_key) || []

      existing = cached_logs.find do |log|
        log[:type] == type &&
        log[:message] == message &&
        (Time.current - log[:timestamp]) < 1.second
      end

      unless existing
        cached_logs << log_entry
        final_logs = cached_logs.last(50)
        Rails.cache.write(cache_key, final_logs, expires_in: 2.hours)
      end
    rescue => e
      Rails.logger.error "🔍 [DEBUG_CACHE] Cache error: #{e.message}"
    end
  end

  def self.get_debug_info(user_id)
    cache_key = "playground_debug_#{user_id}"

    begin
      debug_info = Rails.cache.read(cache_key) || []
      debug_info
    rescue => e
      Rails.logger.error "🔍 [DEBUG_CACHE] Cache read error: #{e.message}"
      []
    end
  end

  def self.clear_debug_info(user_id)
    begin
      Rails.cache.delete("playground_debug_#{user_id}")
    rescue => e
      Rails.logger.error "🔍 [DEBUG_CACHE] Cache clear error: #{e.message}"
    end
  end

  def chat_stream(messages, object, process_id, session_id, system_message_content: nil, lesson_object: nil, session: nil)
    channel = "playground_chat_#{object.to_user_id}_#{session_id}"

    Rails.logger.info "PlaygroundChat streaming to channel: #{channel}"
    Rails.cache.write(process_id, true)

    request_custom_ai(messages, object, system_message_content: system_message_content, lesson_object: lesson_object, channel: channel, session: session) do |content, reasoning_content, chunk|
      if chunk.content.present? || chunk.reasoning_content.present?
        is_generating = Rails.cache.read(process_id)
        if is_generating
          ActionCable.server.broadcast channel, {
            message_id: object.id,
            content: ApplicationController.helpers.emojify(
              UtilityHelper.markdown_to_html(
                UtilityHelper.replace_outside_code_tags(content)
              )
            ).html_safe,
            reasoning_content: reasoning_content,
            finished: false
          }
        else
          if object.class.name == "Message"
            object.update(content: content, reasoning_content: reasoning_content)
          else
            object.update(body: content)
          end
          break
        end
      end
    end

    is_generating = Rails.cache.read(process_id)

    ActionCable.server.broadcast channel, {
      message_id: object.id,
      content: "Finished",
      finished: true
    }

    is_generating
  end

  def request_custom_ai(messages, object = nil, system_message_content: nil, lesson_object: nil, channel: nil, session: nil)
    @current_session = session

    @debug_user_id = Thread.current[:current_user]&.id
    Thread.current[:debug_user_id] = @debug_user_id

    unless is_supported_object?(object)
      yield "エラー: LessonまたはExamオブジェクトのみサポートされています。", "エラー: LessonまたはExamオブジェクトのみサポートされています。"
      return
    end

    school = get_school_from_object(object)

    ai_platform_school = school.ai_platform_school

    if ai_platform_school.blank? || ai_platform_school.api_key.blank? || ai_platform_school.model.blank?
      yield "AI model not configured yet.", "AI model not configured yet."
      return
    end

    config = RubyLLM::Configuration.new
    config.anthropic_api_key = ai_platform_school.api_key
    config.openai_api_key = ai_platform_school.api_key
    config.gemini_api_key = ai_platform_school.api_key
    config.deepseek_api_key = ai_platform_school.api_key
    model = ai_platform_school.model

    chat = RubyLLM.chat(model: model, context: RubyLLM::Context.new(config))

    user_id = object&.to_user_id
    if user_id
      current_user = User.find(user_id) rescue nil
      Thread.current[:current_user] = current_user
    end

    context_object = lesson_object || object

    agent_type = determine_agent_type(context_object)

    add_function_calling_tools(chat, school, agent_type)
    Rails.logger.info "Added function calling tools for agent type: #{agent_type}"

    if system_message_content.present?
      system_message = RubyLLM::Message.new(
        role: :system,
        content: system_message_content
      )
      chat.add_message(system_message)
    end

    if lesson_object.present?
      add_lesson_context_for_playground(chat, lesson_object, messages.where(owner_type: 'student').last&.content, school)
    elsif get_lesson_from_object(object)
      add_lesson_context(chat, object, messages.where(owner_type: 'student').last&.content, school)
    end

    messages.each do |message|
      attachments = school.using_deepseek? ? [] : message.attachments.map(&:url)

      chat_message = RubyLLM::Message.new(
        role: message.bot? ? :assistant : :user,
        content: RubyLLM::Content.new(
          message.content,
          attachments
        )
      )

      chat.add_message(chat_message)
    end

    content = ""
    reasoning_content = ""
    if (msg = messages.where(owner_type: "bot").last) && (prompt = chat.messages.select { |m| m.role == :system }.map(&:content).join("\n\n")).present?
      begin
        cleaned_prompt = prompt.gsub(/<ctx>(.*?)<\/ctx>/m, '')

        truncated_prompt = cleaned_prompt.length > 1_000_000 ?
          cleaned_prompt[0..999_000] + "\n\n[TRUNCATED - Original length: #{cleaned_prompt.length} chars]" :
          cleaned_prompt

        msg.update(system_prompt: truncated_prompt)
        Rails.logger.info "Saved system prompt (#{truncated_prompt.length} chars) to message #{msg.id}"
        user_input = messages.where(owner_type: "student").last&.content || "No user input"
        PlaygroundChat.add_debug_info(nil, 'user_input', "User Input", user_input)
        PlaygroundChat.add_debug_info(nil, 'system_prompt', "System prompt generated", {
          message_id: msg.id,
          prompt_length: truncated_prompt.length,
          was_truncated: cleaned_prompt.length > 1_000_000,
          original_length: cleaned_prompt.length,
          system_prompt: truncated_prompt
        })

      rescue => e
        Rails.logger.error "Failed to save system prompt to message #{msg.id}: #{e.message}"

        PlaygroundChat.add_debug_info(nil, 'error', "System prompt save failed", {
          message_id: msg&.id,
          error_message: e.message,
          error_class: e.class.name
        })
      end
    end

    chat.complete do |chunk|
      if chunk.content
        content += chunk.content
        yield content, reasoning_content, chunk
      end

      if chunk.reasoning_content
        reasoning_content += chunk.reasoning_content
        yield content, reasoning_content, chunk
      end
    end
  rescue => e
    Rails.logger.error "Error in PlaygroundChat.request_custom_ai: #{e.message}"
    puts "Error in PlaygroundChat.request_custom_ai: #{e.message}"
    yield "エラーが発生しました。", "エラーが発生しました。"
  ensure
    Thread.current[:current_user] = nil

    return unless object
    return "" if content.blank?

    attachments = charts_from_content(content)
    if attachments.present?
      md = attachments.map { |a| "![draw](#{a.url})" }.join("\n")
      yield content + "\n" + md, md
    end

    if attachments.present?
      if object.class.name == "Message"
        object.update(content: content, reasoning_content: reasoning_content, attachment_ids: attachments.map(&:id))
      else
        object.update(body: content, attachment_ids: attachments.map(&:id))
      end
    else
      if object.class.name == "Message"
        object.update(content: content, reasoning_content: reasoning_content)
      else
        object.update(body: content)
      end
    end

    content
  end

  private

  def is_supported_object?(object)
    object && (object.is_a?(Lesson) || object.is_a?(Exam) || (object.respond_to?(:targetable_type) && object.targetable_type == "Lesson"))
  end

  def determine_agent_type(object)
    if object.is_a?(Lesson)
      'lesson'
    elsif object.is_a?(Exam)
      'exam'
    elsif object.respond_to?(:targetable_type)
      case object.targetable_type
      when "Lesson"
        'lesson'
      when "Exam"
        'exam'
      else
        'lesson' # Default fallback
      end
    else
      'lesson' # Default fallback
    end
  end

  def get_school_from_object(object)
    if object.is_a?(Lesson)
      object.school
    elsif object.is_a?(Exam)
      object.school
    elsif object.respond_to?(:targetable_type)
      case object.targetable_type
      when "Lesson"
        lesson = Lesson.find(object.targetable_id)
        lesson.school
      when "Exam"
        exam = Exam.find(object.targetable_id)
        exam.school
      else
        raise "School not found for targetable type: #{object.targetable_type}"
      end
    else
      raise "Unsupported object type: #{object.class.name}"
    end
  end

  def get_lesson_from_object(object)
    if object.is_a?(Lesson)
      object
    elsif object.respond_to?(:targetable_type) && object.targetable_type == "Lesson"
      Lesson.find(object.targetable_id)
    else
      nil
    end
  end

  def add_lesson_context_for_playground(chat, lesson, user_input, school)
    return unless lesson.is_a?(Lesson)

    Rails.logger.info "Adding playground lesson context for lesson: #{lesson.name} (ID: #{lesson.id})"

    lesson_context = "現在のレッスン情報:\n"
    lesson_context += "レッスン名: #{lesson.name}\n"
    lesson_context += "レッスンID: #{lesson.id}\n"
    lesson_context += "説明: #{lesson.description}\n" if lesson.description.present?
    lesson_context += "\nFunction Callingでこのレッスン情報を取得する場合は、lesson_id: #{lesson.id} を使用してください。"

    context_message = RubyLLM::Message.new(
      role: :system,
      content: lesson_context
    )
    chat.add_message(context_message)
  end

  def add_lesson_context(chat, object, user_input, school)
    lesson = get_lesson_from_object(object)
    return unless lesson

    lesson_context = get_lesson_rag_context(user_input, lesson, school)

    lesson_agent = school.ai_tutor_agents.find_by(agent_type: 'lesson')
    agent_context = get_agent_rag_context(user_input, lesson_agent, school)
    combined_context = [lesson_context, agent_context].reject(&:blank?).join("\n\n---\n\n")

    lesson_context_message = RubyLLM::Message.new(
      role: :system,
      content: "あなたはAIアシスタントです。生徒の質問に答えてください。画像に関する質問がある場合は、提供された画像の説明を参照してください。Pineconeのコンテキスト情報は補足的に使用し、質問に直接関係ない場合は無視してください。常に簡潔で的確な回答を心がけてください。\n
Pineconeコンテキスト: #{combined_context}"
    )

    chat.add_message(lesson_context_message)
  end

  def get_lesson_rag_context(query, lesson, school)
    return "" if query.blank?

    begin
      lesson_agent = school.ai_tutor_agents.find_by(agent_type: 'lesson')
      return "" unless lesson_agent

      rag_config = lesson_agent.ai_tutor_rag_configs.first
      return "" unless rag_config&.enabled?

      search_params = rag_config.search_params_for({
        lesson_id: lesson.id
      })

      result = AiTutorRagService.search_materials(
        query: query,
        school: school,
        **search_params
      )

      if result[:success] && result[:results].present?
        contexts = result[:results].map do |item|
          item[:text] || item['text']
        end.compact

        return contexts.join("\n\n")
      else
        Rails.logger.info "No lesson RAG context found for query: #{query}"
        return ""
      end
    rescue => e
      Rails.logger.error "Error getting lesson RAG context in playground: #{e.message}"
      return ""
    end
  end

  def get_agent_rag_context(query, agent, school)
    return "" if query.blank?

    begin
      rag_config = agent.ai_tutor_rag_configs.first
      return "" unless rag_config&.enabled?

      search_params = rag_config.search_params_for({})

      result = AiTutorRagService.search_materials(
        query: query,
        school: school,
        **search_params
      )

      if result[:success] && result[:results].present?
        contexts = result[:results].map do |item|
          item[:text] || item['text']
        end.compact

        return contexts.join("\n\n")
      else
        Rails.logger.info "No agent RAG context found for query: #{query}"
        return ""
      end
    rescue => e
      Rails.logger.error "Error getting agent RAG context in playground: #{e.message}"
      return ""
    end
  end

  def add_function_calling_tools(chat, school, agent_type)
    begin
      agent = school.ai_tutor_agents.find_by(agent_type: agent_type)
      return unless agent

      agent.ai_tutor_tools.enabled.each_with_index do |tool, index|
        begin
          dynamic_tool_class = create_dynamic_tool_class(tool)
        rescue => e
          Rails.logger.error "❌ [PLAYGROUND] Error creating tool class for #{tool.tool_name}: #{e.message}"
          Rails.logger.error "❌ [PLAYGROUND] Error backtrace: #{e.backtrace.first(3).join("\n")}"

          PlaygroundChat.add_debug_info(nil, 'error', "Failed to create tool: #{tool.tool_name}", {
            tool_name: tool.tool_name,
            error_message: e.message,
            error_class: e.class.name,
            backtrace: e.backtrace.first(3)
          })

          dynamic_tool_class = nil
        end

        if dynamic_tool_class
          PlaygroundChat.add_debug_info(nil, 'tool_added', "Tool added: #{tool.tool_name}", {
            tool_name: tool.tool_name,
            tool_type: tool.tool_type,
            description: tool.description,
            when_to_use: tool.when_to_use
          })

          chat.with_tool(dynamic_tool_class)
        else
          PlaygroundChat.add_debug_info(nil, 'error', "Tool creation failed: #{tool.tool_name}", {
            tool_name: tool.tool_name,
            error_message: "Dynamic tool class creation returned nil",
            possible_causes: [
              "Function definition parsing failed",
              "Method signature not recognized",
              "Invalid function syntax"
            ]
          })
        end
      end
    rescue => e
      Rails.logger.error "Error in add_function_calling_tools: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end

  def create_dynamic_tool_class(custom_tool)
    return nil if custom_tool.function_definition.blank?

    method_info = parse_function_definition(custom_tool.function_definition)
    unless method_info
      Rails.logger.error "Failed to parse function definition for #{custom_tool.tool_name}"

      PlaygroundChat.add_debug_info(nil, 'error', "Function parsing failed: #{custom_tool.tool_name}", {
        tool_name: custom_tool.tool_name,
        error_message: "Could not parse function definition",
        function_definition_preview: custom_tool.function_definition[0..200] + (custom_tool.function_definition.length > 200 ? "..." : ""),
        expected_format: "def method_name or def method_name(params)"
      })

      return nil
    end

    tool_class = Class.new(RubyLLM::Tool) do
      full_description = custom_tool.description
      if custom_tool.when_to_use.present?
        full_description += "\n When to use: #{custom_tool.when_to_use}"
      end

      PlaygroundChat.add_debug_info(nil, 'tool_created', "Tool class created: #{custom_tool.tool_name}", {
        full_description: full_description,
        method_name: method_info[:method_name],
        params: method_info[:params]
      })

      description full_description

      method_info[:params].each do |param_name, param_info|
        next if param_name == 'user_id'
        param param_name.to_sym,
              desc: param_info[:description] || param_name.to_s,
              required: param_info[:required] != false
      end

      @function_definition = custom_tool.function_definition
      @method_name = method_info[:method_name]

      define_method :execute do |**params|
        begin
          execution_context = create_execution_context

          execution_context.instance_eval(self.class.instance_variable_get(:@function_definition))

          method_name = self.class.instance_variable_get(:@method_name)
          if execution_context.respond_to?(method_name, true)
            result = execution_context.send(method_name, **params)

            PlaygroundChat.add_debug_info(nil, 'tool_result', "Success: #{custom_tool.tool_name}", {
              result: result
            })

            result
          else
            error_msg = "Method #{method_name} not found in function definition"
            { error: error_msg }
          end
        rescue => e
          PlaygroundChat.add_debug_info(nil, 'error', "Error in #{custom_tool.tool_name}: #{e.message}", {
            error_class: e.class.name,
            backtrace: e.backtrace.first(3)
          })

          { error: "Function execution failed: #{e.message}" }
        end
      end

      private

      define_method :create_execution_context do
        context = Object.new

        context.define_singleton_method(:User) { ::User }
        context.define_singleton_method(:School) { ::School }
        context.define_singleton_method(:Lesson) { ::Lesson }
        context.define_singleton_method(:Exam) { ::Exam }
        context.define_singleton_method(:Rails) { ::Rails }

        context.define_singleton_method(:current_user) { Thread.current[:current_user] }

        context.define_singleton_method(:days) { |n| n.days }
        context.define_singleton_method(:weeks) { |n| n.weeks }
        context.define_singleton_method(:months) { |n| n.months }
        context.define_singleton_method(:ago) { |duration| duration.ago }

        context
      end
    end

    tool_class.define_singleton_method(:name) { "CustomTool_#{custom_tool.tool_name}" }

    tool_class
  rescue => e
    Rails.logger.error "Failed to create dynamic tool class for #{custom_tool.tool_name}: #{e.message}"
    nil
  end

  def parse_function_definition(code)
    method_match = code.match(/def\s+(\w+)(?:\s*\(([^)]*)\))?/)
    return nil unless method_match

    method_name = method_match[1]
    params_string = method_match[2] || ""

    params = {}
    if params_string.present?
      params_string.split(',').each do |param|
        param = param.strip
        if param.include?(':')
          parts = param.split(':')
          param_name = parts[0].strip
          default_value = parts[1]&.strip
          params[param_name] = {
            description: param_name.humanize,
            required: !default_value || default_value == 'nil'
          }
        else
          param_name = param.gsub(/[^a-zA-Z0-9_]/, '')
          params[param_name] = {
            description: param_name.humanize,
            required: true
          }
        end
      end
    end

    {
      method_name: method_name,
      params: params
    }
  rescue => e
    Rails.logger.error "Failed to parse function definition: #{e.message}"
    nil
  end

  def charts_from_content(content)
    content.split("```").select do |child|
      text = child.strip
      text.starts_with?("python") && text.ends_with?("show()") && text.include?("import matplotlib.pyplot")
    end.map do |child|
      nil
    end.compact
  end
end
