require 'net/http'
require 'json'
include ActionView::Helpers::SanitizeHelper
include ActionView::Helpers::TextHelper
module CustomChat
  def self.send_exam_generation_steam(messages, histories, exam, user, process_id)
    channel = "ChatExamGenerationChannel_user:#{user.id}:#{exam.id}:chats"

    Rails.cache.write(process_id, true)

    content = CustomChat.request_chat_gpt(messages, histories, nil, endpoint: "/exams/") do | content, chunk |
      # runningチェック
      is_generating = Rails.cache.read(process_id)
      break if is_generating == false

      ActionCable.server.broadcast channel, {exam_id: exam.id, content: content, chunk: chunk, finished: false}
    end
    ActionCable.server.broadcast channel, {exam_id: exam.id, content: content, chunk: '', finished: true}
  end

  def self.chat_steam(messages, object, process_id, system_message_content: nil)
    Rails.cache.write(process_id, true)
    channel = "user:#{object.to_user_id}:#{object.targetable_type}:#{object.targetable_id}:chats"
    CustomChat.request_custom_ai(messages, object, system_message_content: system_message_content) do |content, reasoning_content, chunk|
      if chunk.content.present? || chunk.reasoning_content.present?
        is_generating = Rails.cache.read(process_id)
        if is_generating
          ActionCable.server.broadcast channel, {
            message_id: object.id,
            content: ApplicationController.helpers.emojify(UtilityHelper.markdown_to_html UtilityHelper.replace_outside_code_tags(content)).html_safe,
            reasoning_content: reasoning_content
          }
        else
          if object.class.name == "Message"
            object.update(content: content, reasoning_content: reasoning_content)
          else
            object.update(body: content)
          end
        end
      end
    end

    # runningチェック
    is_generating = Rails.cache.read(process_id)
    is_generating

    ActionCable.server.broadcast "user:#{object.to_user_id}:#{object.targetable_type}:#{object.targetable_id}:chats", {
      message_id: object.id,
      content: "Finished"
    }
  end

  def self.request_chat_gpt(messages, histories = " ", object = nil, endpoint: nil)
    content = ""
    if Rails.env.production?
      langchain_host = ENV["LANGCHAIN_HOST"]
    else
      langchain_host = "http://0.0.0.0:8000"
    end
    uri = URI.parse(langchain_host)
    hostname = uri.hostname
    port = uri.port
    uri.path = endpoint
    req = Net::HTTP::Post.new(uri)
    body = {
      "message": messages,
      "memory": histories
    }
    req.body = body.to_json
    req.content_type = "application/json"
    should_continue = true
    begin
      res = Net::HTTP.start(hostname, port) do |http|
        http.request(req) do |response|
          response.read_body do |chunk|
            chunk = chunk.force_encoding("utf-8")
            content = content + chunk

            should_continue = yield content, chunk

            break unless should_continue
          end
          break unless should_continue
        end
        break unless should_continue
      end
    rescue => e
      Rails.logger.error "Error in chat_steam: #{e.message}"
    ensure
      if object.class.name == "Message"
        object.update(content: content)
      else
        object.update(body: content) if object
      end
    end
    content
  end

  def self.request_custom_ai(messages, object = nil, system_message_content: nil)
    school = case object.targetable_type
             when "Lesson"
               lesson = Lesson.find(object.targetable_id)
               lesson.school
             when "Question"
               question = Q.find(object.targetable_id)
               question.exams.first.school
             when "Exam"
               exam = Exam.find(object.targetable_id)
               exam.school
             else
               raise "School not found for targetable type: #{object.targetable_type}"
             end


    ai_platform_school = school.ai_platform_school

    if ai_platform_school.blank? || ai_platform_school.api_key.blank? || ai_platform_school.model.blank?
      yield "AI model not configured yet.", "AI model not configured yet."
      return
    end

    config = RubyLLM::Configuration.new
    config.anthropic_api_key = ai_platform_school.api_key
    config.openai_api_key = ai_platform_school.api_key
    config.gemini_api_key = ai_platform_school.api_key
    config.deepseek_api_key = ai_platform_school.api_key
    model = ai_platform_school.model

    chat = RubyLLM.chat(model: , context: RubyLLM::Context.new(config))

    if system_message_content.present?
      system_message = RubyLLM::Message.new(
        role: :system,
        content: system_message_content
      )

      chat.add_message(system_message)
    end

    if object.targetable_type == "Lesson"
      last_message = messages.last
      last_message_content = last_message.content
      pinecone =  get_message_pinecone_context(last_message_content, object.targetable_id)&.uniq || ""

      system_message = RubyLLM::Message.new(
        role: :system,
        content: "あなたはAIアシスタントです。生徒の質問に答えてください。画像に関する質問がある場合は、提供された画像の説明を参照してください。Pineconeのコンテキスト情報は補足的に使用し、質問に直接関係ない場合は無視してください。常に簡潔で的確な回答を心がけてください。\n
  Pineconeコンテキスト: #{pinecone}"
      )

      chat.add_message(system_message)
    end

    messages.each do |message|
      attachments = school.using_deepseek? ? [] : message.attachments.map(&:url)

      chat_message = RubyLLM::Message.new(
        role: message.bot? ? :assistant : :user,
        content: RubyLLM::Content.new(
          message.content,
          attachments
        )
      )

      chat.add_message(chat_message)
    end

    content = ""
    reasoning_content = ""
    chat.complete do |chunk|
      if chunk.content
        content += chunk.content

        yield content, reasoning_content, chunk
      end

      if chunk.reasoning_content
        reasoning_content += chunk.reasoning_content

        yield content, reasoning_content, chunk
      end
    end
  rescue => e
    Rails.logger.error "Errror in request_custom_ai: #{ e.message }"
    puts "Error in request_custom_ai: #{ e.message }"

    yield "エラーが発生しました。", "エラーが発生しました。"
  ensure
   return unless object
   return "" if content.blank?

   attachments = charts_from_content(content)
   if attachments.present?
     md = attachments.map { |a| "![draw](#{a.url})" }.join("\n")
     yield content + "\n" + md, md
   end

   if attachments.present?
     if object.class.name == "Message"
       object.update(content: content, reasoning_content: reasoning_content, attachment_ids: attachments.map(&:id))
     else
       object.update(body: content, attachment_ids: attachments.map(&:id))
     end
   else
     if object.class.name == "Message"
       object.update(content: content, reasoning_content: reasoning_content)
     else
       object.update(body: content)
     end
   end

   content
  end

  def self.request_chat_gpt_without_langchain(messages, object = nil, endpoint: "/chats/")
    if object.targetable_type == Lesson.name
      last_message = messages.last
      last_message_content = last_message["content"]
      last_message_content.each do |item|
        if item["type"] == "text"
          context = get_message_pinecone_context(item["text"], object.targetable_id).uniq
          if context.present?
            item["text"] = %{#{item["text"]}

Pineconeコンテキスト: #{context}}
          end
        end
      end
    end

    content = ""
    if Rails.env.production?
      langchain_host = ENV["LANGCHAIN_HOST"]
    else
      langchain_host = "http://0.0.0.0:8000"
    end
    uri = URI.parse(langchain_host)
    hostname = uri.hostname
    port = uri.port
    uri.path = endpoint
    req = Net::HTTP::Post.new(uri)
    body = { "messages": messages }
    # if object.class.name == "Message"
    #   body["lesson_id"] = object.targetable_id.to_s
    # end
    req.body = body.to_json
    req.content_type = "application/json"
    should_continue = true

    begin
      res = Net::HTTP.start(hostname, port) do |http|
        http.request(req) do |response|
          response.read_body do |chunk|
            chunk = chunk.force_encoding("utf-8")
            content = content + chunk

            should_continue = yield content, chunk

            break unless should_continue
          end
          break unless should_continue
        end
        break unless should_continue
      end
    ensure
      return unless object

      attachments = charts_from_content(content)
      if attachments.present?
        md = attachments.map { |a| "![draw](#{a.url})" }.join("\n")
        yield content + "\n" + md, md
      end

      if attachments.present?
        if object.class.name == "Message"
          object.update(content: content, attachment_ids: attachments.map(&:id))
        else
          object.update(body: content, attachment_ids: attachments.map(&:id))
        end
      else
        if object.class.name == "Message"
          object.update(content: content)
        else
          object.update(body: content)
        end
      end
    end
    content
  end

  def self.get_message_pinecone_context(content, lesson_id)
    if Rails.env.production?
      langchain_host = ENV["LANGCHAIN_HOST"]
    else
      langchain_host = "http://0.0.0.0:8000"
    end
    uri = URI.parse(langchain_host)
    hostname = uri.hostname
    port = uri.port
    uri.path = "/chats/get_pinecone"
    req = Net::HTTP::Post.new(uri)
    body = { "query": content, "lesson_id": lesson_id.to_s }
    req.body = body.to_json
    req.content_type = "application/json"
    result = nil
    begin
      res = Net::HTTP.start(hostname, port) do |http|
        http.request(req)
      end

      result = JSON.parse(res.body)["context"]
    rescue => e
      Rails.logger.error "Error in chat_steam: #{e.message}"
    end

    result
  end

  def self.grading(messages)
    if Rails.env.production?
      langchain_host = ENV["LANGCHAIN_HOST"]
    else
      langchain_host = "http://0.0.0.0:8000"
    end
    uri = URI.parse(langchain_host)
    hostname = uri.hostname
    port = uri.port
    uri.path = "/exams/grading"
    req = Net::HTTP::Post.new(uri)
    req.body = messages.to_json
    req.content_type = "application/json"
    begin
      res = Net::HTTP.start(hostname, port) do |http|
        http.request(req)
      end

      result = res.body
    rescue => e
      raise "Error in grading: #{e.message}"
    end

    result
  end

  def self.charts_from_content(content)
    content.split("```").select do |child|
      text = child.strip
      text.starts_with?("python") && text.ends_with?("show()") && text.include?("import matplotlib.pyplot")
    end.map do |child|
      source = child.gsub(/\A.*\n/,'')
      image_body = draw_python_chart(source)
      Attachment.create_from_string(image_body, filename: "draw.png") if image_body
    end.compact
  end

  def self.draw_python_chart(source)
    if Rails.env.production?
      langchain_host = ENV["LANGCHAIN_HOST"]
    else
      langchain_host = "http://0.0.0.0:8000"
    end
    uri = URI.parse(langchain_host)
    hostname = uri.hostname
    port = uri.port
    uri.path = "/chats/draw/"
    req = Net::HTTP::Post.new(uri)
    req.body = { "source": source }.to_json
    req.content_type = "application/json"
    res = Net::HTTP.start(hostname, port) do |http|
      http.request(req)
    end

    if res.header["Content-type"] == "image/png"
      res.body
    else
      nil
    end
  end
end
