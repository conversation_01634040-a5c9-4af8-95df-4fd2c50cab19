
module BaseChat
  extend self

  def chat_stream_with_channel(messages, object, process_id, channel, system_message_content: nil, &ai_request_block)
    Rails.cache.write(process_id, true)

    ai_request_block.call(messages, object, system_message_content) do |content, reasoning_content, chunk|
      if chunk.content.present? || chunk.reasoning_content.present?
        is_generating = Rails.cache.read(process_id)
        if is_generating
          ActionCable.server.broadcast channel, {
            message_id: object.id,
            content: ApplicationController.helpers.emojify(
              UtilityHelper.markdown_to_html(
                UtilityHelper.replace_outside_code_tags(content)
              )
            ).html_safe,
            reasoning_content: reasoning_content,
            finished: false
          }
        else
          update_object_content(object, content, reasoning_content)
          break
        end
      end
    end

    is_generating = Rails.cache.read(process_id)

    ActionCable.server.broadcast channel, {
      message_id: object.id,
      content: "Finished",
      finished: true
    }

    is_generating
  end

  def setup_ai_config(school)
    ai_platform_school = school.ai_platform_school

    if ai_platform_school.blank? || ai_platform_school.api_key.blank? || ai_platform_school.model.blank?
      return nil
    end

    config = RubyLLM::Configuration.new
    config.anthropic_api_key = ai_platform_school.api_key
    config.openai_api_key = ai_platform_school.api_key
    config.gemini_api_key = ai_platform_school.api_key
    config.deepseek_api_key = ai_platform_school.api_key

    {
      config: config,
      model: ai_platform_school.model,
      school: school
    }
  end

  def setup_function_calling_tools(chat, school, object)
    user_id = object&.to_user_id
    if user_id
      current_user = User.find(user_id) rescue nil
      Thread.current[:current_user] = current_user
    end

    add_function_calling_tools(chat, school, object)
  end

  def process_messages_for_chat(chat, messages, school)
    messages.each do |message|
      attachments = school.using_deepseek? ? [] : message.attachments.map(&:url)

      chat_message = RubyLLM::Message.new(
        role: message.bot? ? :assistant : :user,
        content: RubyLLM::Content.new(
          message.content,
          attachments
        )
      )

      chat.add_message(chat_message)
    end
  end

  def add_system_message(chat, system_message_content)
    if system_message_content.present?
      system_message = RubyLLM::Message.new(
        role: :system,
        content: system_message_content
      )
      chat.add_message(system_message)
    end
  end

  private

  def update_object_content(object, content, reasoning_content)
    if object.class.name == "Message"
      object.update(content: content, reasoning_content: reasoning_content)
    else
      object.update(body: content)
    end
  end

  def add_function_calling_tools(chat, school, object)
    school.ai_tutor_tools.enabled.each do |tool|
      begin
        Rails.logger.info "Adding tool: #{tool.tool_name}"
        dynamic_tool_class = create_dynamic_tool_class(tool)
        if dynamic_tool_class
          chat.with_tool(dynamic_tool_class)
        end
      rescue => e
        Rails.logger.error "Failed to load tool #{tool.tool_name}: #{e.message}"
      end
    end
  end

  def create_dynamic_tool_class(custom_tool)
    return nil if custom_tool.function_definition.blank?

    method_info = parse_function_definition(custom_tool.function_definition)
    return nil unless method_info

    tool_class = Class.new(RubyLLM::Tool) do
      # Combine description with when_to_use for better LLM context
      full_description = custom_tool.description
      if custom_tool.when_to_use.present?
        full_description += "\n使用場面: #{custom_tool.when_to_use}"
      end
      description full_description

      method_info[:params].each do |param_name, param_info|
        next if param_name == 'user_id'
        param param_name.to_sym,
              desc: param_info[:description] || param_name.to_s,
              required: param_info[:required] != false
      end

      @function_definition = custom_tool.function_definition
      @method_name = method_info[:method_name]

      define_method :execute do |**params|
        begin
          execution_context = create_execution_context

          if method_info[:params].key?('user_id')
            params[:user_id] = Thread.current[:current_user]&.id
          end

          result = execution_context.instance_exec(**params) do |**args|
            eval(@function_definition)
            send(@method_name, **args)
          end

          case result
          when Hash, Array
            result.to_json
          else
            result.to_s
          end
        rescue => e
          Rails.logger.error "Error executing custom tool #{custom_tool.tool_name}: #{e.message}"
          "エラーが発生しました: #{e.message}"
        end
      end

      define_method :create_execution_context do
        context = Object.new

        context.define_singleton_method(:User) { ::User }
        context.define_singleton_method(:School) { ::School }
        context.define_singleton_method(:Lesson) { ::Lesson }
        context.define_singleton_method(:Exam) { ::Exam }
        context.define_singleton_method(:Rails) { ::Rails }

        context.define_singleton_method(:current_user) { Thread.current[:current_user] }

        context.define_singleton_method(:days) { |n| n.days }
        context.define_singleton_method(:weeks) { |n| n.weeks }
        context.define_singleton_method(:months) { |n| n.months }
        context.define_singleton_method(:ago) { |duration| duration.ago }

        context
      end
    end

    tool_class.define_singleton_method(:name) { "CustomTool_#{custom_tool.tool_name}" }
    tool_class
  rescue => e
    Rails.logger.error "Failed to create dynamic tool class for #{custom_tool.tool_name}: #{e.message}"
    nil
  end

  def parse_function_definition(code)
    method_match = code.match(/def\s+(\w+)\s*\(([^)]*)\)/)
    return nil unless method_match

    method_name = method_match[1]
    params_string = method_match[2]

    params = {}
    if params_string.present?
      params_string.split(',').each do |param|
        param = param.strip
        if param.include?(':')
          parts = param.split(':')
          param_name = parts[0].strip
          default_value = parts[1]&.strip

          params[param_name] = {
            required: default_value.blank?,
            default: default_value,
            description: "Parameter #{param_name}"
          }
        else
          params[param] = {
            required: true,
            description: "Parameter #{param}"
          }
        end
      end
    end

    {
      method_name: method_name,
      params: params
    }
  rescue => e
    Rails.logger.error "Failed to parse function definition: #{e.message}"
    nil
  end
end
