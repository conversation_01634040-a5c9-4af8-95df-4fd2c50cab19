module PromptVariables
  extend self

  def initialize_context(object, messages)
    @object = object
    @messages = messages
    @lesson = nil
    @exam = nil
    @question = nil
    @user = messages.last&.user
  end

  def user_name
    @user&.name || 'User has no name'
  end

  def user_profile
    return '' unless @user

    profile_parts = []
    profile_parts << "名前: #{@user.name}" if @user.name.present?
    profile_parts << "学年: #{@user.grade}" if @user.respond_to?(:grade) && @user.grade.present?
    profile_parts << "レベル: #{@user.level}" if @user.respond_to?(:level) && @user.level.present?

    profile_parts.join("\n")
  end

  def lesson_title
    ensure_lesson_loaded
    @lesson&.name || 'Lesson has no title'
  end

  def lesson_body
    ensure_lesson_loaded
    @lesson&.body || 'Lesson has no body'
  end

  def lesson_id
    ensure_lesson_loaded
    @lesson&.id&.to_s || 'Lesson has no id'
  end

  def materials
    ensure_lesson_loaded
    return 'Lesson has no materials' unless @lesson&.materials&.present?

    @lesson.materials.map(&:filename).join(', ')
  end

  def exam_title
    ensure_exam_loaded
    @exam&.name || 'Exam has no title'
  end

  def exam_description
    ensure_exam_loaded
    @exam&.description || 'Exam has no description'
  end

  def exam_id
    ensure_exam_loaded
    @exam&.id&.to_s || 'Exam has no id'
  end

  def test_title
    ensure_question_loaded
    @question&.exams&.first&.name || 'Question has no title'
  end

  def test_description
    ensure_question_loaded
    @question&.exams&.first&.description || 'Question has no description'
  end

  def test_id
    ensure_question_loaded
    @question&.exams&.first&.id&.to_s || 'Question has no id'
  end

  def goal_title
    ensure_goal_loaded
    @goal&.title || 'Goal has no title'
  end

  def goal_description
    ensure_goal_loaded
    @goal&.description || 'Goal has no description'
  end

  def goal_id
    ensure_goal_loaded
    @goal&.id&.to_s || 'Goal has no id'
  end

  def progress
    ensure_goal_loaded
    return 'Goal has no progress' unless @goal

    "進捗: #{@goal.progress_percentage}%"
  end

  def recent_activities
    return 'User has no recent activities' unless @user

    activities = @user.activities.recent.limit(5)
    return 'User has no recent activities' if activities.empty?

    activities.map do |activity|
      "#{activity.created_at.strftime('%Y/%m/%d')}: #{activity.description}"
    end.join("\n")
  end

  private

  def ensure_lesson_loaded
    return if @lesson.present? || @object.targetable_type != "Lesson"

    @lesson = Lesson.find(@object.targetable_id)
  end

  def ensure_exam_loaded
    return if @exam.present? || @object.targetable_type != "Exam"

    @exam = Exam.find(@object.targetable_id)
  end

  def ensure_question_loaded
    return if @question.present? || @object.targetable_type != "Question"

    @question = Q.find(@object.targetable_id)
  end

  def ensure_goal_loaded
    return if @goal.present? || @object.targetable_type != "Goal"

    @goal = Goal.find(@object.targetable_id)
  end
end
