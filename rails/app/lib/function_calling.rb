module FunctionCalling
  extend self

  def default_tools(school: nil)
    base_tools = [
      {
        tool_name: 'search_learning_history',
        tool_type: 'search',
        description: 'Searches the student\'s learning history (lessons and courses).',
        when_to_use: 'Use when a student wants to review their past learning history or check their learning status for a specific period or subject.',
        function_definition: search_learning_history_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'get_user_weaknesses',
        tool_type: 'analysis',
        description: 'Analyzes student weaknesses based on performance data.',
        when_to_use: 'Use when you want to identify a student\'s weak areas, find points for improvement, or determine a direction for individualized instruction.',
        function_definition: get_user_weaknesses_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'check_learning_progress',
        tool_type: 'progress',
        description: 'Comprehensively checks learning progress and statistics.',
        when_to_use: 'Use when you want to comprehensively evaluate a student\'s learning progress, check learning statistics or achievement rates, or review a learning plan.',
        function_definition: check_learning_progress_definition,
        enabled: true,
        is_default: true
      }
    ]

    return base_tools unless school

    base_tools.map do |tool|
      custom_tool = find_custom_tool_setting(school, tool[:tool_name])
      if custom_tool&.when_to_use.present?
        tool.merge(when_to_use: custom_tool.when_to_use)
      else
        tool
      end
    end
  end

  private

  def find_custom_tool_setting(school, tool_name)
    school.ai_tutor_tools.find_by(tool_name: tool_name)
  end

  def search_learning_history_definition
    <<~RUBY
      def search_learning_history(days: 30)
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")
        start_date = days.to_i.zero? ? 30.day.ago : days.to_i.days.ago

        enrollment_lessons = user.enrollment_lessons
                                .joins(:lesson)
                                .where("enrollment_lessons.created_at >= ?", start_date)
                                .includes(:lesson, :enrollment)
                                .order(created_at: :desc)
                                .limit(50)

        enrollments = user.enrollments
                         .joins(:course)
                         .where("enrollments.created_at >= ?", start_date)
                         .includes(:course)
                         .order(created_at: :desc)
                         .limit(20)

        results = []

        enrollment_lessons.each do |enrollment_lesson|
          results << {
            type: 'lesson',
            date: enrollment_lesson.created_at.strftime("%Y-%m-%d %H:%M"),
            title: enrollment_lesson.lesson.name,
            status: enrollment_lesson.finished_at ? '完了' : '進行中',
            count: enrollment_lesson.count || 0,
            finished_at: enrollment_lesson.finished_at&.strftime("%Y-%m-%d %H:%M"),
            course: enrollment_lesson.enrollment.course.name
          }
        end

        enrollments.each do |enrollment|
          results << {
            type: 'course',
            date: enrollment.created_at.strftime("%Y-%m-%d %H:%M"),
            title: enrollment.course.name,
            status: enrollment.finished? ? '完了' : '受講中',
            lessons_count: enrollment.enrollment_lessons_count || 0,
            progress_rate: enrollment.progress_rate || 0
          }
        end

        results.sort_by { |r| r[:date] }.reverse
      rescue => e
        { error: "学習履歴の取得に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def get_user_weaknesses_definition
    <<~RUBY
      def get_user_weaknesses
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        # Analyze lesson performance
        weak_lessons = user.enrollment_lessons
                          .joins(:lesson)
                          .where("finished_at IS NULL OR count < ?", 3)
                          .includes(:lesson, :enrollment)
                          .limit(10)

        lesson_weaknesses = weak_lessons.map do |enrollment_lesson|
          count = enrollment_lesson.count || 0
          days_since_start = (Time.current - enrollment_lesson.created_at) / 1.day

          difficulty_level = if count == 0 && days_since_start > 7
                              'とても難しい'
                            elsif count < 2 && days_since_start > 3
                              '難しい'
                            elsif count < 5
                              '普通'
                            else
                              '簡単'
                            end

          {
            lesson_name: enrollment_lesson.lesson.name,
            count: count,
            status: enrollment_lesson.finished_at ? 'ゆっくり完了' : '未完了',
            course: enrollment_lesson.enrollment.course.name,
            difficulty_level: difficulty_level,
            created_at: enrollment_lesson.created_at.strftime("%Y-%m-%d")
          }
        end

        # Analyze course performance
        weak_courses = user.enrollments
                          .joins(:course)
                          .where("enrollment_lessons_count < ?", 5)
                          .includes(:course)
                          .limit(5)

        course_weaknesses = weak_courses.select { |e| !e.finished? }.map do |enrollment|
          {
            course_name: enrollment.course.name,
            lessons_completed: enrollment.enrollment_lessons_count || 0,
            progress_rate: enrollment.progress_rate || 0,
            status: enrollment.finished? ? '完了' : '進行中',
            recommendation: "このコースのレッスンをもっと完了しましょう",
            enrolled_at: enrollment.created_at.strftime("%Y-%m-%d")
          }
        end

        overall_recommendation = if lesson_weaknesses.any? || course_weaknesses.any?
                                  "未完了のレッスンや進捗の遅いコースを復習しましょう"
                                else
                                  "全体的に良い進捗です！この調子で学習を続けましょう"
                                end

        {
          lesson_weaknesses: lesson_weaknesses,
          course_weaknesses: course_weaknesses,
          overall_recommendation: overall_recommendation,
          analysis_date: Time.current.strftime("%Y-%m-%d")
        }
      rescue => e
        { error: "弱点分析に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def check_learning_progress_definition
    <<~RUBY
      def check_learning_progress(period: "month")
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        start_date = case period
                     when "week" then 1.week.ago
                     when "month" then 1.month.ago
                     when "year" then 1.year.ago
                     else 1.month.ago
                     end

        enrollment_lessons = user.enrollment_lessons.where("enrollment_lessons.created_at >= ?", start_date)
        completed_lessons = enrollment_lessons.where.not(finished_at: nil)

        enrollments = user.enrollments.where("enrollments.created_at >= ?", start_date)
        completed_courses = enrollments.select(&:finished?)

        total_lesson_count = enrollment_lessons.sum(:count) || 0

        avg_lesson_completion = enrollment_lessons.any? ? (completed_lessons.count.to_f / enrollment_lessons.count * 100) : 0
        avg_course_completion = enrollments.any? ? (completed_courses.size.to_f / enrollments.count * 100) : 0

        # Calculate daily average
        days = case period
               when "week" then 7
               when "month" then 30
               when "year" then 365
               else 30
               end

        # Calculate learning streak
        current_date = Date.current
        streak = 0

        while current_date >= start_date.to_date
          has_activity = user.enrollment_lessons
                            .where(enrollment_lessons: { created_at: current_date.beginning_of_day..current_date.end_of_day })
                            .exists?

          if has_activity
            streak += 1
            current_date -= 1.day
          else
            break
          end
        end

        # Find most active day
        activity_by_day = enrollment_lessons
                           .group("DATE(enrollment_lessons.created_at)")
                           .count

        most_active_day = if activity_by_day.empty?
                           "活動なし"
                         else
                           most_active = activity_by_day.max_by { |date, count| count }
                           "\#{most_active[0]}（\#{most_active[1]}回の活動）"
                         end

        {
          period: period,
          start_date: start_date.strftime("%Y-%m-%d"),
          end_date: Time.current.strftime("%Y-%m-%d"),
          total_lessons_started: enrollment_lessons.count,
          completed_lessons: completed_lessons.count,
          lesson_completion_rate: enrollment_lessons.count > 0 ? ((completed_lessons.count.to_f / enrollment_lessons.count) * 100).round(1) : 0,
          average_lesson_completion: avg_lesson_completion.round(1),
          total_courses_started: enrollments.count,
          completed_courses: completed_courses.size,
          course_completion_rate: enrollments.count > 0 ? ((completed_courses.size.to_f / enrollments.count) * 100).round(1) : 0,
          average_course_completion: avg_course_completion.round(1),
          total_lesson_count: total_lesson_count,
          average_daily_lessons: (total_lesson_count / days.to_f).round(1),
          overall_progress_score: ((avg_lesson_completion * 0.6) + (avg_course_completion * 0.4)).round(1),
          learning_streak: streak,
          most_active_day: most_active_day
        }
      rescue => e
        { error: "学習進捗の取得に失敗しました: \#{e.message}" }
      end
    RUBY
  end
end
