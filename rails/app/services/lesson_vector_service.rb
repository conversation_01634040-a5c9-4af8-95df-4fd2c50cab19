# Service để xử lý vector generation cho lessons sử dụng Rails thuần túy
# Thay thế việc gọi Python API
class LessonVectorService
  include ActiveSupport::Benchmarkable

  attr_reader :lesson, :pinecone_service, :errors

  def initialize(lesson)
    @lesson = lesson
    @errors = []
    @pinecone_service = create_pinecone_service
  end

  # Main method để generate vectors cho lesson
  def generate_vectors!
    Rails.logger.info "Starting vector generation for lesson #{lesson.id}: #{lesson.name}"

    return false unless validate_prerequisites

    begin
      benchmark "Vector generation for lesson #{lesson.id}" do
        # 1. Xóa vectors cũ nếu có
        delete_existing_vectors

        # 2. <PERSON><PERSON><PERSON> bị content
        content = prepare_lesson_content
        return false if content.blank?

        # 3. Split text thành chunks
        chunks = split_text_into_chunks(content)
        Rails.logger.info "Split content into #{chunks.length} chunks"

        # 4. Generate embeddings cho từng chunk
        vectors = generate_embeddings_for_chunks(chunks)
        return false if vectors.empty?

        # 5. Upload vectors lên <PERSON>
        upload_vectors_to_pinecone(vectors)

        # 6. Update timestamp
        lesson.update_column(:vector_generated_at, Time.zone.now)

        Rails.logger.info "Successfully generated #{vectors.length} vectors for lesson #{lesson.id}"
        true
      end
    rescue => e
      Rails.logger.error "Vector generation failed for lesson #{lesson.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      @errors << e.message
      false
    end
  end

  # Method để query vectors (cho testing)
  def query_vectors(query_text, top_k: 5)
    return [] unless pinecone_service&.configured?

    # Generate embedding cho query
    query_embedding = generate_single_embedding(query_text)
    return [] if query_embedding.blank?

    # Query Pinecone
    filter = { "source" => lesson.id.to_s }
    pinecone_service.query(
      query_embedding,
      top_k: top_k,
      filter: filter,
      include_metadata: true
    )
  end

  # Method để xóa vectors của lesson
  def delete_vectors!
    return true unless pinecone_service&.configured?

    Rails.logger.info "Deleting vectors for lesson #{lesson.id}"
    filter = { "source" => lesson.id.to_s }
    pinecone_service.delete_by_metadata(filter)
  end

  def has_vectors?
    return false unless pinecone_service&.configured?

    results = query_vectors("test", top_k: 1)
    results.any?
  end

  def vector_stats
    return {} unless pinecone_service&.configured?

    results = query_vectors("test", top_k: 100)
    {
      total_vectors: results.length,
      lesson_id: lesson.id,
      lesson_name: lesson.name,
      last_generated: lesson.vector_generated_at,
      has_vectors: results.any?
    }
  end

  private

  def create_pinecone_service
    school = lesson.course&.school
    return nil unless school

    agent = school.ai_tutor_agents.first
    return nil unless agent

    PineconeService.for_agent(agent)
  end

  def validate_prerequisites
    unless lesson
      @errors << "Lesson not found"
      return false
    end

    unless pinecone_service&.configured?
      @errors << "Pinecone not configured"
      return false
    end

    content = prepare_lesson_content
    if content.blank?
      @errors << "Lesson has no content to vectorize"
      return false
    end

    true
  end

  def prepare_lesson_content
    content = case lesson.body_type
              when 0 # Markdown
                lesson.md_body.to_s
              when 1 # CKEditor
                lesson.ck_body.to_s
              else
                ""
              end

    full_content = "#{lesson.name}\n\n#{content}".strip

    if full_content.present?
      host = "https://#{ENV["DEFAULT_DOMAIN"]}"
      if lesson.course&.school&.subdomain.present?
        host = "https://#{lesson.course.school.subdomain}.#{ENV["DEFAULT_DOMAIN"]}"
      end
      lesson_url = "#{host}/classroom/my-courses/#{lesson.course&.id}?lesson_id=#{lesson.id}"
      full_content += "\n参考レッスンのURL: #{lesson_url}"
    end

    full_content
  end

  def split_text_into_chunks(content)
    chunks = []
    chunk_size = 1000
    chunk_overlap = 20

    words = content.split(/\s+/)
    current_chunk = []
    current_length = 0

    words.each do |word|
      word_length = word.length + 1

      if current_length + word_length > chunk_size && current_chunk.any?
        chunks << current_chunk.join(" ")

        overlap_words = current_chunk.last([chunk_overlap, current_chunk.length].min)
        current_chunk = overlap_words + [word]
        current_length = current_chunk.join(" ").length
      else
        current_chunk << word
        current_length += word_length
      end
    end

    chunks << current_chunk.join(" ") if current_chunk.any?

    chunks.reject(&:blank?)
  end

  def generate_embeddings_for_chunks(chunks)
    Rails.logger.info "Generating embeddings for #{chunks.length} chunks"

    vectors = []
    chunks.each_with_index do |chunk, index|
      embedding = generate_single_embedding(chunk)
      next if embedding.blank?

      vector_id = "lesson_#{lesson.id}_chunk_#{index}_#{SecureRandom.hex(4)}"
      metadata = {
        "course_id" => lesson.course&.id.to_s,
        "source" => lesson.id.to_s,
        "school_id" => lesson.course&.school_id.to_s,
        "lesson_title" => lesson.name,
        "chunk" => index,
        "text" => chunk
      }

      vectors << {
        id: vector_id,
        values: embedding,
        metadata: metadata
      }

      Rails.logger.debug "Generated embedding for chunk #{index}/#{chunks.length}"
    end

    Rails.logger.info "Generated #{vectors.length} embeddings successfully"
    vectors
  end

  def generate_single_embedding(text)
    return [] if text.blank?

    begin
      config = RubyLLM::Configuration.new
      config.openai_api_key = ENV['OPENAI_ACCESS_TOKEN']
      context = RubyLLM::Context.new(config)

      embedding = RubyLLM.embed(text, context: context)
      embedding.vectors
    rescue => e
      Rails.logger.error "Failed to generate embedding: #{e.message}"
      []
    end
  end

  def delete_existing_vectors
    Rails.logger.info "Deleting existing vectors for lesson #{lesson.id}"
    filter = { "source" => lesson.id.to_s }
    pinecone_service.delete_by_metadata(filter)
  end

  def upload_vectors_to_pinecone(vectors)
    return false if vectors.empty?

    Rails.logger.info "Uploading #{vectors.length} vectors to Pinecone"

    batch_size = 100
    vectors.each_slice(batch_size).with_index do |batch, batch_index|
      Rails.logger.debug "Uploading batch #{batch_index + 1}/#{(vectors.length.to_f / batch_size).ceil}"

      pinecone_service.index.upsert(
        vectors: batch,
        namespace: pinecone_service.namespace
      )
    end

    Rails.logger.info "Successfully uploaded all vectors to Pinecone"
    true
  end
end
