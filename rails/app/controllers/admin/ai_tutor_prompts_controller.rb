class Admin::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ptsController < Admin::ApplicationController
  before_action :set_school
  before_action :set_ai_tutor_agent, if: -> { params[:ai_tutor_agent_id] }
  before_action :set_ai_tutor_prompt, only: [:show, :edit, :update, :destroy]

  def index
    @ai_tutor_prompts = @ai_tutor_agent.ai_tutor_prompts.order(:prompt_type)
    @prompt_types = [@ai_tutor_agent.agent_type]

    @prompts_by_type = {}
    @ai_tutor_prompts.each do |prompt|
      @prompts_by_type[prompt.prompt_type] = prompt
    end

  end

  def show
  end

  def new
    @ai_tutor_prompt = @ai_tutor_agent.ai_tutor_prompts.build
    @prompt_types = [@ai_tutor_agent.agent_type]
  end

  def create
    @ai_tutor_prompt = @ai_tutor_agent.ai_tutor_prompts.build(ai_tutor_prompt_params)
    @ai_tutor_prompt.school = @school

    if @ai_tutor_prompt.save
      redirect_to admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent),
                  notice: 'AI Tutor Prompt was successfully created.'
    else
      @prompt_types = [@ai_tutor_agent.agent_type]
      render :new
    end
  end

  def edit
    @prompt_types = AiTutorPrompt::PROMPT_TYPES
  end

  def update
    if @ai_tutor_prompt.update(ai_tutor_prompt_params)
      redirect_to admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent),
                  notice: 'AI Tutor Prompt was successfully updated.'
    else
      @prompt_types = [@ai_tutor_agent.agent_type]
      render :edit
    end
  end

  def destroy
    redirect_path = @ai_tutor_agent ?
      admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent) :
      admin_school_ai_tutor_prompts_path(@school)

    @ai_tutor_prompt.destroy
    redirect_to redirect_path, notice: 'AI Tutor Prompt was successfully deleted.'
  end

  # AJAX endpoint to update prompt content
  def update_content
    prompt_type = params[:prompt_type]
    content = params[:content]
    enabled = true  # Always enabled

    # Validate required params
    if prompt_type.blank? || content.blank?
      render json: {
        status: 'error',
        message: 'Prompt type and content are required'
      }, status: 422
      return
    end

    # Find or create the prompt
    if @ai_tutor_agent
      @ai_tutor_prompt = @ai_tutor_agent.ai_tutor_prompts.find_or_initialize_by(
        prompt_type: prompt_type
      )
      @ai_tutor_prompt.school = @school if @ai_tutor_prompt.new_record?
    else
      @ai_tutor_prompt = @school.ai_tutor_prompts.find_or_initialize_by(
        prompt_type: prompt_type
      )
    end

    @ai_tutor_prompt.assign_attributes(
      content: content,
      enabled: enabled
    )

    if @ai_tutor_prompt.save
      render json: {
        status: 'success',
        message: 'Prompt updated successfully',
        prompt: {
          id: @ai_tutor_prompt.id,
          prompt_type: @ai_tutor_prompt.prompt_type,
          content: @ai_tutor_prompt.content,
          enabled: @ai_tutor_prompt.enabled
        }
      }
    else
      render json: {
        status: 'error',
        message: @ai_tutor_prompt.errors.full_messages.join(', ')
      }, status: 422
    end
  end



  def playground_chat
    user_message = params[:message]
    prompt_content = params[:prompt_content]
    prompt_type = params[:prompt_type] || 'lesson'
    session_id = params[:session_id] || SecureRandom.uuid
    lesson_id = params[:lesson_id]

    if user_message.blank? || prompt_content.blank?
      render json: {
        status: 'error',
        message: 'Message and prompt content are required'
      }, status: 422
      return
    end

    begin
      chat_thread = find_or_create_playground_thread(session_id, prompt_type)

      user_msg = create_playground_user_message(chat_thread, user_message, prompt_type)

      bot_msg = create_playground_bot_message(chat_thread, prompt_type)

      render json: {
        status: 'success',
        user_message_id: user_msg.id,
        bot_message_id: bot_msg.id,
        session_id: session_id
      }

      current_session = session

      Thread.new do
        begin
          playground_chat_real_stream(chat_thread, bot_msg, prompt_content, session_id, lesson_id, current_session)
        rescue => e
          Rails.logger.error "Playground chat streaming error: #{e.message}"
          Rails.logger.error e.backtrace.join("\n")

          bot_msg.update(content: 'エラーが発生しました。もう一度お試しください。')

          ActionCable.server.broadcast "playground_chat_#{current_user.id}_#{session_id}", {
            type: 'error',
            message_id: bot_msg.id,
            content: 'エラーが発生しました。もう一度お試しください。',
            finished: true
          }
        end
      end

    rescue => e
      Rails.logger.error "Playground chat error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      render json: {
        status: 'error',
        message: 'チャット処理中にエラーが発生しました',
        error: e.message
      }, status: 500
    end
  end

  def debug_info
    debug_logs = PlaygroundChat.get_debug_info(current_user&.id)

    formatted_logs = debug_logs.map do |log|
      {
        timestamp: log[:timestamp].strftime('%H:%M:%S'),
        type: log[:type],
        message: log[:message],
        data: log[:data]
      }
    end

    render json: {
      status: 'success',
      debug_logs: formatted_logs
    }
  end

  def clear_debug
    PlaygroundChat.clear_debug_info(current_user&.id)
    render json: { status: 'success', message: 'Debug info cleared' }
  end

  private

  def set_school
    @school = School.find(params[:school_id])
  end

  def set_ai_tutor_agent
    @ai_tutor_agent = @school.ai_tutor_agents.find(params[:ai_tutor_agent_id])
  end

  def set_ai_tutor_prompt
    if @ai_tutor_agent
      @ai_tutor_prompt = @ai_tutor_agent.ai_tutor_prompts.find(params[:id])
    else
      @ai_tutor_prompt = @school.ai_tutor_prompts.find(params[:id])
    end
  end

  def ai_tutor_prompt_params
    params.require(:ai_tutor_prompt).permit(:prompt_type, :content, :enabled)
  end

  def playground_chat_real_stream(chat_thread, bot_message, prompt_content, session_id, lesson_id = nil, current_session = nil)
    messages = chat_thread.messages.order(:created_at)

    process_id = "generating_message_#{current_user.id}_playground_#{bot_message.id}"

    temp_prompt = AiTutorPrompt.new(
      prompt_type: 'lesson',
      content: prompt_content,
      school: @school,
      enabled: true
    )

    system_message_content = AiTutorPromptRenderer.build_system_message(
      temp_prompt,
      bot_message,
      messages
    )

    lesson_object = lesson_id.present? ? @school.lessons.find_by(id: lesson_id) : nil

    PlaygroundChat.chat_stream(
      messages,
      bot_message,
      process_id,
      session_id,
      system_message_content: system_message_content,
      lesson_object: lesson_object,
      session: current_session
    )
  end

  def find_or_create_playground_thread(session_id, prompt_type)
    targetable_type = 'Lesson'
    targetable = @school.lessons.first

    thread = ChatThread.find_by(name: "Playground-#{session_id}")

    unless thread
      thread = ChatThread.create!(
        user_id: current_user.id,
        school_id: @school.id,
        targetable_id: targetable.id,
        targetable_type: targetable_type,
        name: "Playground-#{session_id}"
      )
    end

    thread
  end

  def create_playground_user_message(chat_thread, content, prompt_type)
    Message.create!(
      user_id: current_user.id,
      school_id: @school.id,
      targetable_id: chat_thread.targetable_id,
      targetable_type: chat_thread.targetable_type,
      chat_thread_id: chat_thread.id,
      content: content,
      owner_type: Message.owner_types["student"],
      skip_callback: true
    )
  end

  def create_playground_bot_message(chat_thread, prompt_type)
    teacher_id = @school.teachers.first&.user_id || current_user.id

    Message.create!(
      user_id: teacher_id,
      school_id: @school.id,
      targetable_id: chat_thread.targetable_id,
      targetable_type: chat_thread.targetable_type,
      chat_thread_id: chat_thread.id,
      content: "",
      to_user_id: current_user.id,
      owner_type: Message.owner_types["bot"],
      skip_callback: true
    )
  end
end
