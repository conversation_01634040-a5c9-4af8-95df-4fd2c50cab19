class Admin::<PERSON><PERSON><PERSON>r<PERSON>oolsController < Admin::ApplicationController
  before_action :set_school
  before_action :set_ai_tutor_agent
  before_action :set_ai_tutor_tool, only: [:show, :edit, :update, :destroy, :toggle_enabled]

  def index
    @all_tools = AiTutorTool.all_tools_for_agent(@ai_tutor_agent)
    @tool_types = AiTutorTool::TOOL_TYPES

    @tools_by_type = @all_tools.group_by(&:tool_type)
  end

  def show
  end

  def new
    @ai_tutor_tool = @ai_tutor_agent.ai_tutor_tools.build
  end

  def create
    @ai_tutor_tool = @ai_tutor_agent.ai_tutor_tools.build(ai_tutor_tool_params)
    @ai_tutor_tool.school = @school

    if @ai_tutor_tool.save
      redirect_to admin_school_ai_tutor_agent_ai_tutor_tools_path(@school, @ai_tutor_agent),
                  notice: 'Tool was successfully created.'
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @ai_tutor_tool.update(ai_tutor_tool_params)
      redirect_to admin_school_ai_tutor_agent_ai_tutor_tools_path(@school, @ai_tutor_agent),
                  notice: 'Tool was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    if @ai_tutor_tool&.is_default?
      redirect_to admin_school_ai_tutor_agent_ai_tutor_tools_path(@school, @ai_tutor_agent),
                  alert: 'Cannot delete default tools.'
    else
      @ai_tutor_tool.destroy
      redirect_to admin_school_ai_tutor_agent_ai_tutor_tools_path(@school, @ai_tutor_agent),
                  notice: 'Tool was successfully deleted.'
    end
  end

  def toggle_enabled
    return render(json: {status: 'error', message: 'Tool not found'}, status: 404) unless @ai_tutor_tool

    @ai_tutor_tool.update!(enabled: !@ai_tutor_tool.enabled)
    render json: {
      status: 'success',
      enabled: @ai_tutor_tool.enabled,
      message: @ai_tutor_tool.enabled ? 'Tool enabled' : 'Tool disabled'
    }
  rescue => e
    render json: {status: 'error', message: e.message}, status: 422
  end

  def update_when_to_use
    return render(json: {status: 'error', message: 'Tool not found'}, status: 404) unless @ai_tutor_tool
    @ai_tutor_tool.update!(when_to_use: params[:when_to_use])
    render json: {status: 'success', message: 'when_to_use updated successfully'}
  rescue => e
    render json: {status: 'error', message: e.message}, status: 422
  end

  def rag_config
    config = {
      pinecone_api_key: ENV['PINECONE_API_KEY'].present?,
      pinecone_environment: RENV['PINECONE_ENVIRONMENT'] || 'gcp-starter',
      pinecone_index_name: ENV['PINECONE_INDEX_NAME'] || 'edbase-materials',
      openai_api_key: ENV['OPENAI_API_KEY'].present?
    }

    render json: {
      status: 'success',
      config: config
    }
  end

  private

  def set_school
    @school = School.find(params[:school_id])
  end

  def set_ai_tutor_agent
    @ai_tutor_agent = @school.ai_tutor_agents.find(params[:ai_tutor_agent_id])
  end

  def set_ai_tutor_tool
    @ai_tutor_tool = @ai_tutor_agent.ai_tutor_tools.find(params[:id])
  end

  def ai_tutor_tool_params
    permitted_params = [:when_to_use, :enabled]
    permitted_params += [:tool_name, :tool_type, :description, :configuration, :function_definition] unless @ai_tutor_tool&.is_default?
    params.require(:ai_tutor_tool).permit(permitted_params)
  end
end
