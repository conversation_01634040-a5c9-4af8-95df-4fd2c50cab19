class Admin::CoursesController < Admin::ApplicationController
  # :nocov:
  load_resource :school
  before_action :school_onboarding_detect
  authorize_resource :school, if: -> {@school.present?}
  load_and_authorize_resource through: :school, if: -> {@school.present?}
  load_resource :goal
  authorize_resource :goal, if: -> {@goal.present?}
  load_resource if: -> {current_user.is_admin? && @school.blank?}
  load_resource :user
  load_resource :catalog
  load_resource :premium_service
  load_resource :user_onboard
  before_action :set_mode
  layout "admin_material"
  skip_before_action :verify_authenticity_token, only: [:import, :import_info]
  before_action :set_course_tags, only: [:show, :index, :new, :edit, :setting]
  before_action :ensure_course_question_enabled, only: :questions

  add_breadcrumb -> { "講座一覧" }, path: [:admin, :@school, :courses]

  def index
    per_page = params[:per_page] ? params[:per_page] : Settings.paginations.default_perpage_new
    render_form = "admin/enrollments/search_courses"
    if params[:add_catalog] == "true"
      @catalogs = @school.catalogs
    else
      @courses = @courses.find_with_name(params[:q]) if params[:q].present?
      @courses = Course.find_with_context(params[:context], @courses)
      @courses = @courses.by_type(params["type_is"]&.split(",")) if params["type_is"].present?
    end
    if params[:published] == "true"
      @courses = @courses.published
    end
    if params[:published] == "false"
      @courses = @courses.not_publish
    end
    if params[:tag]
      @courses = @courses.tagged_with(params[:tag])
    end
    @courses = @courses.includes(:course_purchases).page(params[:page]).per(per_page).order(created_at: :desc)
    if(params[:pickup_resources].present?)
      @selected_course_ids = @school.pickup_resources.courses.pluck(:objectionable_id)
    end
    @tags = @school.courses.tag_counts
    @schools = School.all
    copying_course_ids = CopyProcess.where(copied_resource_school_id: @school.id, status: :copying).pluck(:origin_resource_id)
    @copying_courses = Course.where(id: copying_course_ids)
    respond_to do |format|
      format.html
      format.js do
        if params[:add_catalog].present?
          render_form = "admin/users/search_catalogs"
        elsif params[:catalog_id].present?
          render_form = "admin/catalogs/search_courses"
        elsif params[:premium_service_id].present?
          render_form = "admin/premium_services/search_courses"
        elsif params[:user_onboard_id].present?
          render_form = "admin/user_onboards/search_courses"
        elsif params[:user_id].present?
          render_form = "admin/users/search_courses"
        elsif params[:pickup_resources].present?
          render_form = "admin/pickup_resources/_search"
        elsif params[:copy_course].present?
          render_form = "admin/courses/search_courses"
        end
        render render_form
      end
      if params[:add_catalog].present?
        format.json { render json: @catalogs.map { |catalog| {value: catalog.id, label: catalog.name} }}
      else
        format.json { render json: @courses.map { |course| {value: course.id, label: course.name} }}
      end
    end
  end

  def all
    per_page = params[:per_page] ? params[:per_page] : Settings.paginations.default_perpage_new
    @schools = School.all
    if params[:from_school_id]
      school = School.find(params[:from_school_id])
      @courses = school.courses
    else
      @courses = @school.courses
    end
    @courses = @courses.find_with_name(params[:q]) if params[:q].present?
    @courses = @courses.includes(:course_purchases).page(params[:page]).per(per_page).order(created_at: :desc)
    respond_to do |format|
      format.html
      format.js do
        render "admin/courses/search_courses"
      end
    end
  end

  def copy_from_other_school
    if params[:course_id]
      course_id = params[:course_id]
      duplicator = CourseDuplicatorService.new(course_id, @school.id)
      duplicator.call()
      # CoursesDuplicatorWorker.perform_async course_id, @school.id
      copying_course_ids = CopyProcess.where(copied_resource_school_id: @school.id, status: :copying).pluck(:origin_resource_id)
      @copying_courses = Course.where(id: copying_course_ids)
      respond_to do |format|
        format.js
      end
    end
  end

  def show
    redirect_to [:admin, @school, @goal, @course, :lessons]
  end

  def new
    @course = Course.new
    @master_milestone = MasterMilestone.find params[:master_milestone_id] if params[:master_milestone_id]
    @materials = @course.materials.order(created_at: :desc)
    @coupons = @school.coupons.get_available_coupons
  end

  def edit
    @master_milestone = @course.master_milestones.first
    @materials = @course.materials.order(created_at: :desc)
    @coupons = @school.coupons.get_available_coupons
  end

  def create
    @course = @school.courses.build(course_params)
    @course.process = {
      "assumed_learner": false,
      "introduce": false,
      "page_design": false,
      "setting": false,
      "pricing": false,
      "product_recommend": false,
      "publishing": false
    }
    respond_to do |format|
      if @course.save
        # スクールを一回再取得します。
        @school.reload
        @school.complete_onboarding_course
        @school.save!
        Purchase::ProductService.new("Course", @course.id).find_or_create_product

        format.html { redirect_to assumed_learner_admin_school_course_path(@school, @course) }
        format.json { render :assumed_learner, status: :created, location: @course }
      else
        format.html { render :new }
        format.json { render json: @course.errors, status: :unprocessable_entity }
      end
    end
  end

  def assumed_learner
    @course.targets.build if @course.targets.empty?
    @course.learns.build(order: 1) if @course.learns.empty?
    @course.prerequisits.build if @course.prerequisits.empty?
  end

  def save_assumed_learner
    @course.process["assumed_learner"] = true
    respond_to do |format|
      ActiveRecord::Base.transaction do
        if @course.save_assumed_learner(save_assumed_learner_params)
          format.html { redirect_to assumed_learner_admin_school_course_path, notice: "保存しました。" }
          format.js
        else
          format.html { render :assumed_learner }
          format.json { render json: @course.errors, status: :unprocessable_entity }
        end
      end
    end
  end

  def product_recommend
    recommended_product_ids = ProductRecommend.where(
      product_id: @course.product&.id,
      user_id: current_user.id
    ).rank(:row_order).pluck(:recommended_product_id)

    @courses_recommend = Product.where(id: recommended_product_ids)
                          .index_by(&:id)
                          .values_at(*recommended_product_ids)
                          .compact
                          .map(&:productable)

  end

  def save_product_recommend
    @course.update(
      process: @course.process.merge("product_recommend" => true),
      use_product_recommend_user_and_system: params[:use_product_recommend_user_and_system] == "true",
      is_display_product_recommend: params[:is_display_product_recommend] == "true"
    )

    ActiveRecord::Base.transaction do
      # TODO: CONFIRM LOGIC create product
      current_product = Course.find(params[:id]).product

      if params[:product_recommend].present? && params[:product_recommend][:course_ids].present?
        ProductRecommend.where(
          product_id: @course.product&.id,
          user_id: current_user.id
        ).destroy_all

        if current_product.nil?
          current_product = Purchase::ProductService.new("Course", @course.id).find_or_create_product
        end

        @school.courses.where(id: params[:product_recommend][:course_ids]).each do |course|
          ProductRecommend.find_or_create_by!(
            product_id: current_product&.id,
            recommended_product_id: course.product&.id,
            user_id: current_user.id
          )
        end
      end

      respond_to do |format|
        format.html { redirect_to product_recommend_admin_school_course_path(@school, current_product.productable) }
        format.json { render :show, status: :ok }
      end
    end
  end

  def course_sort
    ProductRecommend.where(product_id: @course.product&.id, user_id: current_user.id).each do |product_recommend|
      if Product.find_by_id(product_recommend.recommended_product_id).productable.id == params[:course_id].to_i
        product_recommend.update(params.require(:product_recommend).permit(:row_order))
      end
    end
  end

  def introduce
  end

  def save_introduce
    if params.dig(:course, :image_cache) == "delete"
      @course.remove_image!
    end
    @course.process["introduce"] = true
    respond_to do |format|
      if @course.update(save_introduce_params)
        if params["course"]["save_image"] == "true"
          format.html { render :introduce }
          format.js
        else
          format.html { redirect_to introduce_admin_school_course_path, notice: "保存しました。" }
          format.json { render :introduce, status: :created, location: @course }
          format.js
        end
      else
        format.html { render :introduce }
        format.json { render json: @course.errors, status: :unprocessable_entity }
      end
    end
  end

  def page_design
  end
  def save_page_design
    @course.process["page_design"] = true
    respond_to do |format|
      if @course.update(save_page_design_params)
        format.html { redirect_to page_design_admin_school_course_path, notice: "保存しました。" }
        format.json { render :page_design, status: :created, location: @course }
        format.js
      else
        format.html { render :page_design }
        format.json { render json: @course.errors, status: :unprocessable_entity }
      end
    end
  end

  def setting
    @materials = @course.materials.order(created_at: :desc)
  end

  def save_setting
    @course.process["setting"] = true
    setting_params = save_setting_params
    materials_params = setting_params.delete(:materials)
    respond_to do |format|
      if @course.update(setting_params) && @course.materials.attach(materials_params)
        @materials = @course.materials.order(created_at: :desc)
        if params["course"]["is_upload_file"] == "true"
          format.html { render :setting }
          format.js
        else
          format.html { redirect_to setting_admin_school_course_path, notice: "保存しました。" }
          format.json { render :setting, status: :ok, location: admin_school_goal_course_path(@school, @goal, @course) }
          format.js
        end
      else
        format.html { render :setting }
        format.js   { render :setting }
        format.json { render json: @course.errors, status: :unprocessable_entity }
      end
    end
  end

  def category
    @lists = @school.lists.roots || []
  end

  def save_category
    @course.process["category"] = true
    @course.list_courses.destroy_all
    respond_to do |format|
      if @course.update(save_category_params)
        format.html { redirect_to category_admin_school_course_path, notice: "保存しました。" }
        format.json { render :category, status: :created, location: @course }
        format.js
      else
        format.html { render :category }
        format.json { render json: @course.errors, status: :unprocessable_entity }
      end
    end
  end

  def pricing
  end

  def save_pricing
    @course.process["pricing"] = true
    respond_to do |format|
      if @course.update(save_pricing_params)
        @course.product&.update(price: @course.price)

        format.html { redirect_to pricing_admin_school_course_path, notice: "保存しました。" }
        format.json { render :pricing, status: :created, location: @course }
        format.js
      else
        format.html { render :pricing }
        format.json { render json: @course.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    if params[:course][:image_cache] == "delete"
      @course.remove_image!
    end

    respond_to do |format|
      update_params = course_params.merge({
        schedule_published_at: schedule_published_at
      })

      if @course.update(update_params)
        if params["course"]["is_upload_file"] == "true"
          @materials = @course.materials.order(created_at: :desc)
          format.html { render :edit }
        else
          format.html { redirect_to process_polymophic(url_for([:admin, @school, @goal, @course])), notice: "コースが編集されました" }
          format.json { render :show, status: :ok, location: admin_school_goal_course_path(@school, @goal, @course) }
        end
      else
        format.html { render :edit }
        format.json { render json: @course.errors, status: :unprocessable_entity }
      end
    end
  end

  def purchases
    @purchases = CoursePurchase.where(course_id: @course.id, school_id: @school.id).order(created_at: :desc)
  end

  def publishing
  end

  def save_publishing
    @course.process["publishing"] = true
    respond_to do |format|
      if @course.update(save_publishing_params)
        format.html { redirect_to publishing_admin_school_course_path, notice: "保存しました。" }
        format.json { render :publishing, status: :created, location: @course }
        format.js
      else
        format.html { render :publishing }
        format.json { render json: @course.errors, status: :unprocessable_entity }
        format.js
      end
    end
  end

  def destroy
    @course.destroy
    VimeoLog.remove_not_use_video(@school) if @school.present?
    respond_to do |format|
      format.html { redirect_to process_polymophic(admin_school_courses_path(@school)), notice: t("message.destroy")}
      format.json { head :no_content }
    end
  end

  def sort
    course = Course.find params[:id]
    course.update(course_params)
  end

  def import
    if @course.import_lessons(params[:file], @goal)
      notice = "インポートされました。"
    else
      notice = @course.errors.full_messages.join("\n")
    end

    redirect_back(fallback_location: root_path, notice: notice)
  end

  def questions
    @courses = @school.courses.page(params[:page]).per(30)
  end

  def copy
    new_course = CourseDuplicatorService.new(@course.id, @school.id).allow_same_school.call
    if new_course
      flash[:notice] = "講座がコピーされました。"
    else
      flash[:alert] = "講座コピーが失敗しました。"
    end
    redirect_to url_for([:admin, @school, :courses])
  end

  def import_info
    errors = @course.import_info(params[:file])
    if errors.nil?
      notice = "インポートされました。"
    else
      notice = errors.join("\n")
    end

    redirect_back(fallback_location: root_path, notice: notice)
  end

  # API endpoint to get lessons for a course
  def course_lessons
    @course = @school.courses.find(params[:id])
    lessons = @course.lessons.published.order(:name)

    respond_to do |format|
      format.json do
        render json: lessons.map { |lesson|
          {
            id: lesson.id,
            name: lesson.name,
            description: lesson.description
          }
        }
      end
    end
  end

  private

  def set_mode
    @mode = (params[:mode] || 'ui').to_sym
  end

  def save_assumed_learner_params
    params.require(:course).permit(
      learns_attributes: [
        :name, :order
      ],
      targets_attributes: [
        :name, :order
      ],
      prerequisits_attributes: [
        :name, :order
      ],
    )
  end

  def save_introduce_params
    params.require(:course).permit(
      :name,
      :description,
      :body,
      :title,
      :image,
      :ck_body,
      :body_type
    )
  end

  def save_page_design_params
    page_description = params.require(:course).permit(
      # デザインのため
      :page_description
    )[:page_description]

    data_params = params.require(:course).permit(
      :body
    )
    data_params[:body] = page_description
    data_params
  end

  def save_pricing_params
    pricing_params = params.require(:course).permit(
      :type_is,
      :price,
      :cut_rate,
      :coupon_id,
      :installment_count
    )
    if pricing_params[:type_is] == "free" && pricing_params[:price].to_i != 0
      pricing_params[:price] = 0
    end
    pricing_params
  end

  def save_setting_params
    params.require(:course).permit(
      :duration,
      :term_id,
      :level,
      :teacher_id,
      :tag_list,
      :slug,
      materials: [])
  end

  def save_category_params
    params.require(:course).permit(
      list_courses_attributes: [:id, :list_id]
    )
  end

  def save_publishing_params
    params[:course][:schedule_published_at] = "#{params[:course][:schedule_published_date]} #{params[:course][:schedule_published_time]}"
    params.require(:course).permit(
      :published,
      :publish_global,
      :schedule_published_at
    )
  end

  def course_params
    params.require(:course).permit(
      :name,
      :description,
      :body,
      :image,
      :master_milestones,
      :term_id,
      :coupon_id,
      :published,
      :skill_list,
      :duration,
      :row_order_position,
      :type_is,
      :level,
      :price,
      :tag_list,
      :cut_rate,
      :teacher_id,
      materials: []
    )
  end

  def schedule_published_at
    schedule_published = params.permit(:schedule_published_date, :schedule_published_time)
    return nil unless schedule_published[:schedule_published_date].present? && schedule_published[:schedule_published_time].present?

    Time.zone.parse("#{schedule_published[:schedule_published_date]} #{schedule_published[:schedule_published_time]}")
  end

  def set_course_tags
    @course_tags = @school.nil? ? Course.tag_counts_on(:tags) : @school.courses.tag_counts_on(:tags)
  end

  def ensure_course_question_enabled
    unless @school.use_course_question?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end
end
