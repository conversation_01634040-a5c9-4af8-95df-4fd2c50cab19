class SendChatSteamJob < ApplicationJob
  queue_as :default

  def perform(messages, object, process_id, system_message_content: nil)
    sleep(1)
    Rails.cache.write(process_id, true)
    if object.targetable_type == "Lesson"
      LessonChat.chat_steam(messages, object, process_id, system_message_content: system_message_content)
    # TODO: add other chat steam for other objects
    else
      CustomChat.chat_steam(messages, object, process_id, system_message_content: system_message_content)
    end
  end
end
