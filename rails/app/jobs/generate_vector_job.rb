class GenerateVectorJob < ApplicationJob
  queue_as :default

  def perform(lesson_id)
    lesson = Lesson.find_by(id: lesson_id)
    return unless lesson

    # Set the status to processing
    ActionCable.server.broadcast "vector_generation_channel_#{lesson_id}", {
      status: 'processing',
      message: 'ベクター生成を開始しました...',
      progress: 10
    }

    begin
      # Update progress - starting
      ActionCable.server.broadcast "vector_generation_channel_#{lesson_id}", {
        status: 'processing',
        message: 'レッスンコンテンツを処理中...',
        progress: 30
      }

      # Update progress - generating vectors
      ActionCable.server.broadcast "vector_generation_channel_#{lesson_id}", {
        status: 'processing',
        message: 'ベクターデータを生成中...',
        progress: 60
      }

      # Use Rails-native vector service instead of Python API
      vector_service = LessonVectorService.new(lesson)
      success = vector_service.generate_vectors!

      unless success
        error_message = vector_service.errors.join(", ")
        raise StandardError, "Vector generation failed: #{error_message}"
      end

      # Final update - success
      ActionCable.server.broadcast "vector_generation_channel_#{lesson_id}", {
        status: 'completed',
        message: 'ベクター生成が完了しました',
        progress: 100,
        vector_generated_at: lesson.reload.vector_generated_at.strftime("%Y/%m/%d %H:%M")
      }
    rescue => e
      Rails.logger.error("Vector generation error: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))

      # Error update
      ActionCable.server.broadcast "vector_generation_channel_#{lesson_id}", {
        status: 'error',
        message: "エラーが発生しました: #{e.message}",
        progress: 0
      }
    end
  end
end
