# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore Byebug command history file.
.byebug_history

# CarrierWave uploads
/public/uploads/*
/public/assets/*

# dotenv
/.env
/pops
.DS_Store
/public/assets
/public/packs
/public/packs-test
/node_modules

dump.rdb

 # test reports
coverage
spec/examples.txt
rspec.xml

config/settings.local.yml
config/settings/*.local.yml
config/environments/*.local.yml
config/database.yml
public/assets/
public/packs
node_modules/
vendor/bundle/

# Editor
.idea
.vscode
/public/packs
/public/packs-test
/node_modules
yarn-debug.log*
.yarn-integrity
/storage/*
yarn-error.log 

# schema.rb not push
db/schema.rb
/app/assets/builds/*
!/app/assets/builds/.keep
/app/assets/frontend/*
!/app/assets/frontend/.keep
/app/assets/javascripts/frontend/*
!/app/assets/javascripts/frontend/.keep
/app/assets/stylesheets/frontend/*
!/app/assets/stylesheets/frontend/.keep
.idea
