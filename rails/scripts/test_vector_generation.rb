# Script test vector generation Rails thuần túy trong console
# Copy paste vào Rails console để chạy

# Test với lesson cụ thể
def test_lesson_vector_generation(lesson_id)
  puts "=== Testing Rails-Native Vector Generation for Lesson ID: #{lesson_id} ==="

  lesson = Lesson.find_by(id: lesson_id)
  unless lesson
    puts "❌ Lesson not found with ID: #{lesson_id}"
    return false
  end

  puts "📚 Lesson found:"
  puts "  - Name: #{lesson.name}"
  puts "  - Course: #{lesson.course&.name}"
  puts "  - School: #{lesson.course&.school&.name}"
  puts "  - Body type: #{lesson.body_type == 0 ? 'Markdown' : 'CKEditor'}"
  puts "  - Content length: #{lesson.active_body.length} characters"
  puts "  - Last vector generated: #{lesson.vector_generated_at || 'Never'}"

  # Check if lesson has content
  if lesson.md_body.blank? && lesson.name.blank? && lesson.ck_body.blank?
    puts "⚠️  Warning: Lesson has no content to vectorize"
    return false
  end

  puts "\n🔄 Starting Rails-native vector generation..."

  begin
    # Tạo LessonVectorService instance
    vector_service = LessonVectorService.new(lesson)

    # Check prerequisites
    puts "\n1️⃣ Checking prerequisites..."
    school = lesson.course&.school
    agent = school&.ai_tutor_agents&.first

    puts "  - School: #{school&.name || 'Not found'}"
    puts "  - AI Tutor Agent: #{agent&.name || 'Not found'}"
    puts "  - Pinecone API Key: #{ENV['PINECONE_API_KEY'].present? ? 'Set' : 'Not set'}"
    puts "  - OpenAI API Key: #{ENV['OPENAI_ACCESS_TOKEN'].present? ? 'Set' : 'Not set'}"

    # Test vector generation
    puts "\n2️⃣ Generating vectors..."
    success = vector_service.generate_vectors!

    if success
      puts "✅ Vector generation successful!"

      # Test querying
      puts "\n3️⃣ Testing vector query..."
      results = vector_service.query_vectors("test query", top_k: 3)
      puts "  - Found #{results.length} vectors"

      # Show stats
      puts "\n4️⃣ Vector stats:"
      stats = vector_service.vector_stats
      stats.each { |k, v| puts "  - #{k}: #{v}" }

    else
      puts "❌ Vector generation failed!"
      puts "Errors: #{vector_service.errors.join(', ')}"
    end

    return success

  rescue => e
    puts "❌ Error: #{e.message}"
    puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
    return false
  end
end

# Helper để tìm lessons có content
def find_lessons_with_content(limit = 5)
  puts "🔍 Finding lessons with content..."

  lessons = Lesson.joins(:course)
                 .where.not(md_body: [nil, ''])
                 .or(Lesson.where.not(ck_body: [nil, '']))
                 .or(Lesson.where.not(name: [nil, '']))
                 .limit(limit)

  if lessons.empty?
    puts "❌ No lessons found with content"
    return []
  end

  puts "📚 Found #{lessons.count} lessons with content:"
  lessons.each_with_index do |lesson, index|
    content_length = lesson.active_body.length
    puts "  #{index + 1}. ID: #{lesson.id} | #{lesson.name} | #{content_length} chars | Course: #{lesson.course.name}"
  end

  return lessons
end

# Test với lesson đầu tiên có content
def test_sample_lesson
  lessons = find_lessons_with_content(1)
  return unless lessons.any?

  lesson = lessons.first
  puts "\n🎯 Testing with sample lesson: #{lesson.name} (ID: #{lesson.id})"
  test_lesson_vector_generation(lesson.id)
end

# Test background job
def test_vector_job(lesson_id)
  puts "⚙️ Testing GenerateVectorJob for lesson #{lesson_id}..."
  job = GenerateVectorJob.perform_later(lesson_id)
  puts "📋 Job enqueued with ID: #{job.job_id}"
  puts "💡 Check logs or ActionCable for progress updates"
end

puts "🚀 Rails Vector Generation Tester loaded!"
puts ""
puts "Usage:"
puts "  test_lesson_vector_generation(123)  # Test specific lesson"
puts "  find_lessons_with_content           # Find lessons to test"
puts "  test_sample_lesson                  # Test with first available lesson"
puts "  test_vector_job(123)                # Test background job"
puts ""
puts "Environment:"
puts "  Rails env: #{Rails.env}"
puts "  Pinecone API key: #{ENV['PINECONE_API_KEY'].present? ? 'Set' : 'Not set'}"
puts "  OpenAI API key: #{ENV['OPENAI_ACCESS_TOKEN'].present? ? 'Set' : 'Not set'}"
