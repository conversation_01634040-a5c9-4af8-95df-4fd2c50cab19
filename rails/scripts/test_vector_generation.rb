class VectorGenerationTester
  def self.test_lesson(lesson_id)
    puts "=== Testing Vector Generation for Lesson ID: #{lesson_id} ==="

    lesson = Lesson.find_by(id: lesson_id)
    unless lesson
      puts "Lesson not found with ID: #{lesson_id}"
      return false
    end

    puts "Lesson found:"
    puts "  - Name: #{lesson.name}"
    puts "  - Course: #{lesson.course&.name}"
    puts "  - School: #{lesson.course&.school&.name}"
    puts "  - Body type: #{lesson.body_type == 0 ? 'Markdown' : 'CKEditor'}"
    puts "  - Content length: #{lesson.active_body.length} characters"
    puts "  - Last vector generated: #{lesson.vector_generated_at || 'Never'}"

    if lesson.md_body.blank? && lesson.name.blank? && lesson.ck_body.blank?
      puts "Warning: Lesson has no content to vectorize"
      return false
    end

    puts "\nStarting vector generation process..."

    begin
      puts "\n1. Testing direct PineconeData.send_request..."
      result = test_pinecone_data_direct(lesson)

      puts "\n2. Testing GenerateVectorJob..."
      test_background_job(lesson)

      puts "\n3. Testing Python API connectivity..."
      test_python_api_connectivity

      puts "\nVector generation test completed!"
      return true

    rescue => e
      puts "Error during vector generation: #{e.message}"
      puts "   Backtrace: #{e.backtrace.first(3).join("\n   ")}"
      return false
    end
  end

  private

  def self.test_pinecone_data_direct(lesson)
    puts "   Calling PineconeData.send_request(lesson, 'update')..."

    lesson_content = lesson.body_type == 0 ? lesson.md_body : lesson.ck_body
    host = "https://#{ENV["DEFAULT_DOMAIN"]}"
    if lesson.course&.school&.subdomain.present?
      host = "https://#{lesson.course.school.subdomain}.#{ENV["DEFAULT_DOMAIN"]}"
    end
    lesson_url = "#{host}/classroom/my-courses/#{lesson.course&.id}?lesson_id=#{lesson.id}"
    lesson_content = lesson_content + "\n参考レッスンのURL: #{lesson_url}" if lesson_content.present?

    payload = {
      "course_id": lesson.course&.id.to_s,
      "id": lesson.id.to_s,
      "title": lesson.name,
      "school_id": lesson.course&.school_id,
      "content": lesson_content
    }

    puts "   Payload to be sent:"
    puts "      - Course ID: #{payload[:course_id]}"
    puts "      - Lesson ID: #{payload[:id]}"
    puts "      - Title: #{payload[:title]}"
    puts "      - School ID: #{payload[:school_id]}"
    puts "      - Content length: #{payload[:content].length} chars"

    langchain_host = Rails.env.production? ? ENV["LANGCHAIN_HOST"] : "http://localhost:8000"
    puts "   Python API host: #{langchain_host}"

    PineconeData.send_request(lesson, "update")
    puts "   PineconeData.send_request completed successfully"

    lesson.update_column(:vector_generated_at, Time.zone.now)
    puts "   Updated vector_generated_at: #{lesson.vector_generated_at}"

    return true
  end

  def self.test_background_job(lesson)
    puts "   Enqueuing GenerateVectorJob..."

    job = GenerateVectorJob.perform_later(lesson.id)
    puts "   Job enqueued with ID: #{job.job_id}"
    puts "   Job will run in background"
    puts "   To monitor progress, check ActionCable broadcasts or logs"

    puts "\n   To monitor job progress in console:"
    puts "      ActionCable.server.broadcast 'vector_generation_channel_#{lesson.id}', {status: 'test'}"

    return true
  end

  def self.test_python_api_connectivity
    require 'net/http'
    require 'uri'

    langchain_host = Rails.env.production? ? ENV["LANGCHAIN_HOST"] : "http://localhost:8000"

    begin
      uri = URI.parse("#{langchain_host}/docs")
      http = Net::HTTP.new(uri.host, uri.port)
      http.read_timeout = 5

      response = http.get(uri.request_uri)

      if response.code == "200"
        puts "   Python API is accessible at #{langchain_host}"
        puts "   FastAPI docs available at #{langchain_host}/docs"
      else
        puts "   Python API responded with code: #{response.code}"
      end

    rescue => e
      puts "   Cannot connect to Python API at #{langchain_host}"
      puts "   Make sure Python FastAPI server is running:"
      puts "      cd python/app && python main.py"
      puts "   Error: #{e.message}"
    end
  end

  def self.find_lessons_with_content(limit = 5)
    puts "Finding lessons with content..."

    lessons = Lesson.joins(:course)
                   .where.not(md_body: [nil, ''])
                   .or(Lesson.where.not(ck_body: [nil, '']))
                   .or(Lesson.where.not(name: [nil, '']))
                   .limit(limit)

    if lessons.empty?
      puts "No lessons found with content"
      return []
    end

    puts "Found #{lessons.count} lessons with content:"
    lessons.each_with_index do |lesson, index|
      content_length = lesson.active_body.length
      puts "  #{index + 1}. ID: #{lesson.id} | #{lesson.name} | #{content_length} chars | Course: #{lesson.course.name}"
    end

    return lessons
  end

  def self.test_sample_lesson
    lessons = find_lessons_with_content(1)
    return unless lessons.any?

    lesson = lessons.first
    puts "\nTesting with sample lesson: #{lesson.name} (ID: #{lesson.id})"
    test_lesson(lesson.id)
  end
end

  # Usage examples:
puts "Vector Generation Tester loaded!"
puts ""
puts "Usage examples:"
puts "  VectorGenerationTester.test_lesson(123)           # Test specific lesson"
puts "  VectorGenerationTester.find_lessons_with_content  # Find lessons to test"
puts "  VectorGenerationTester.test_sample_lesson         # Test with first available lesson"
puts ""
puts "Environment info:"
puts "  Rails env: #{Rails.env}"
puts "  Python API host: #{Rails.env.production? ? ENV["LANGCHAIN_HOST"] : "http://localhost:8000"}"
puts "  Pinecone API key: #{ENV['PINECONE_API_KEY'].present? ? 'Set' : 'Not set'}"
puts "  OpenAI API key: #{ENV['OPENAI_API_KEY'].present? ? 'Set' : 'Not set'}"
